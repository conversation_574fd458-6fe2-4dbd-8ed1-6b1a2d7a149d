<template>
  <div class="modern-help-center">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <div class="content-container">
        <!-- 左侧：智能客服区域 -->
        <div class="chatbot-section">
          <div class="chatbot-card">
            <div class="chatbot-header">
              <div class="header-content">
                <div class="avatar-container">
                  <img
                    src="https://img.alicdn.com/imgextra/i2/O1CN01Pda9nq1YDV0mnZ31H_!!6000000003025-54-tps-120-120.apng"
                    alt="客服小九"
                    class="chatbot-avatar"
                  />
                  <div class="status-indicator"></div>
                </div>
                <div class="chatbot-info">
                  <h3 class="chatbot-name">客服小九</h3>
                  <p class="chatbot-status">在线 · 智能助手</p>
                </div>
              </div>
              <div class="header-actions">
                <a-button
                  type="primary"
                  shape="round"
                  icon="message"
                  @click="toggleChatbot"
                  class="chat-button"
                >
                  开始对话
                </a-button>
              </div>
            </div>

            <!-- 智能客服对话框容器 -->
            <div class="chatbot-container" :class="{ 'expanded': chatbotExpanded }">
              <div v-if="chatbotExpanded" id="embedded-chatbot" class="embedded-chatbot">
                <!-- 如果智能客服加载失败，显示简单的聊天界面 -->
                <div v-if="!chatbotLoaded" class="simple-chat">
                  <div class="chat-messages" ref="chatMessages">
                    <div v-for="(message, index) in chatMessages" :key="index" class="message" :class="message.type">
                      <div class="message-content" :class="{ loading: message.loading }">{{ message.content }}</div>
                    </div>
                  </div>
                  <div class="chat-input-area">
                    <a-input
                      v-model="currentMessage"
                      placeholder="请输入您的问题..."
                      @pressEnter="sendMessage"
                      class="chat-input"
                    />
                    <a-button type="primary" @click="sendMessage" class="send-button">
                      <a-icon type="send" />
                    </a-button>
                  </div>
                </div>
              </div>
              <div v-if="!chatbotExpanded" class="chatbot-preview">
                <div class="preview-content">
                  <div class="quick-questions">
                    <h4>常见问题</h4>
                    <div class="question-list">
                      <div
                        class="question-item"
                        v-for="(question, index) in quickQuestions"
                        :key="index"
                        @click="askQuestion(question.prompt)"
                      >
                        <a-icon :type="question.icon" />
                        <span>{{ question.prompt }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：视频教程区域 -->
        <div class="videos-section">
          <div class="section-header">
            <h2 class="section-title">
              <a-icon type="play-circle" />
              视频教程
            </h2>
          </div>

          <a-spin :spinning="loading" class="videos-loading">
            <div v-if="helpList.length > 0" class="videos-grid">
              <div
                class="video-card"
                v-for="(item, index) in helpList"
                :key="item.id"
                @click="handleVideoClick(item)"
              >
                <div class="video-thumbnail">
                  <img
                    v-if="item.coverUrl"
                    :src="item.coverUrl"
                    class="thumbnail-img"
                    :alt="item.helpName"
                  />
                  <div v-else class="default-thumbnail">
                    <a-icon type="video-camera" />
                  </div>
                  <div class="play-overlay">
                    <div class="play-icon">
                      <a-icon type="play-circle" theme="filled" />
                    </div>
                  </div>
                  <div class="video-duration">5:30</div>
                </div>

                <div class="video-info">
                  <h3 class="video-title">{{ item.helpName }}</h3>
                  <p class="video-description">{{ item.description || '暂无描述' }}</p>

                  <!-- 关联文档 -->
                  <div class="related-docs" v-if="getRelatedDocs(item).length > 0">
                    <div class="docs-label">
                      <a-icon type="file-text" />
                      相关文档
                    </div>
                    <div class="docs-tags">
                      <a-tag
                        v-for="doc in getRelatedDocs(item)"
                        :key="doc.id"
                        class="doc-tag"
                        @click.stop="openRelatedDoc(doc.url)"
                      >
                        {{ doc.name }}
                      </a-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <a-empty v-else description="暂无帮助视频" class="empty-state">
              <template slot="image">
                <a-icon type="video-camera" style="font-size: 64px; color: #d9d9d9;" />
              </template>
            </a-empty>
          </a-spin>

          <!-- 分页 -->
          <div class="pagination-wrapper" v-if="helpList.length > 0">
            <a-pagination
              :current="ipagination.current"
              :pageSize="ipagination.pageSize"
              :total="ipagination.total"
              :showTotal="(total) => `共 ${total} 个视频`"
              :pageSizeOptions="['8', '16', '24', '32']"
              showSizeChanger
              @change="handleChange"
              @showSizeChange="handleShowSizeChange"
              class="modern-pagination"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 视频播放弹窗 -->
    <a-modal
      :title="currentHelp.helpName"
      :visible="videoVisible"
      :footer="null"
      @cancel="closeVideo"
      :width="modalWidth"
      :destroyOnClose="true"
      centered
      class="modern-video-modal"
    >
      <div class="modal-video-container">
        <div class="modal-video-wrapper">
          <video
            v-if="currentHelp.videoUrl"
            :src="currentHelp.videoUrl"
            controls
            autoplay
            class="modal-video-player"
            ref="videoPlayer"
          ></video>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'

export default {
  name: 'UserHelpView',
  data() {
    return {
      description: '智能帮助中心',
      // 查询条件
      queryParam: {},
      // 数据集
      helpList: [],
      // 分页参数
      ipagination: {
        current: 1,
        pageSize: 16,
        total: 0
      },
      // 加载状态
      loading: false,
      // 视频播放
      videoVisible: false,
      currentHelp: {},
      // 搜索和筛选
      searchText: '',
      selectedUserType: '',
      modalWidth: '85%',
      // 智能客服相关
      chatbotExpanded: false,
      chatbotInitialized: false,
      chatbotLoaded: false,
      currentMessage: '',
      sessionId: null,
      chatMessages: [
        {
          type: 'bot',
          content: '您好！我是客服小九，很高兴为您服务。请问有什么可以帮助您的吗？'
        }
      ],
      // 快速问题
      quickQuestions: [
        { icon: 'file-word', prompt: '如何做出美观专业的Word方案？' },
        { icon: 'setting', prompt: '如何自定义word模版？' },
        { icon: 'dollar', prompt: '快捷报价怎么用？' },
        { icon: 'picture', prompt: '素材库有什么用？' }
      ]
    }
  },
  created() {
    this.loadData()
  },
  mounted() {
    // 初始化智能客服
    this.initChatbot()
  },
  methods: {
    // 切换智能客服展开状态
    toggleChatbot() {
      this.chatbotExpanded = !this.chatbotExpanded
      if (this.chatbotExpanded && !this.chatbotInitialized) {
        // 延迟一点时间确保DOM已经更新
        setTimeout(() => {
          this.initEmbeddedChatbot()
        }, 100)
      }
    },

    // 快速提问
    askQuestion(question) {
      this.chatbotExpanded = true
      if (!this.chatbotInitialized) {
        this.$nextTick(() => {
          this.initEmbeddedChatbot()
        })
      }
      // 使用简单聊天界面发送问题
      this.$nextTick(() => {
        this.currentMessage = question
        this.sendMessage()
      })
    },

    // 发送消息
    async sendMessage() {
      if (!this.currentMessage.trim()) return

      // 添加用户消息
      this.chatMessages.push({
        type: 'user',
        content: this.currentMessage
      })

      const userMessage = this.currentMessage
      this.currentMessage = ''

      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom()
      })

      // 添加加载状态
      this.chatMessages.push({
        type: 'bot',
        content: '正在思考中...',
        loading: true
      })

      this.$nextTick(() => {
        this.scrollToBottom()
      })

      try {
        // 调用真实的AI接口
        const response = await this.callChatbotAPI(userMessage)

        // 移除加载消息
        this.chatMessages = this.chatMessages.filter(msg => !msg.loading)

        // 添加AI回复
        this.chatMessages.push({
          type: 'bot',
          content: response
        })

        this.$nextTick(() => {
          this.scrollToBottom()
        })
      } catch (error) {
        console.error('调用聊天接口失败:', error)

        // 移除加载消息
        this.chatMessages = this.chatMessages.filter(msg => !msg.loading)

        // 添加错误提示
        this.chatMessages.push({
          type: 'bot',
          content: '抱歉，我暂时无法回答您的问题，请稍后再试或联系人工客服。'
        })

        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }
    },

    // 调用聊天机器人API
    async callChatbotAPI(message) {
      const endpoint = "https://webchat-bot-jm-mrhlyvmibi.cn-hangzhou.fcapp.run/chat"

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: message,
          // 可以添加其他需要的参数
          sessionId: this.getSessionId(),
          timestamp: Date.now()
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      // 根据接口返回的数据结构调整
      return data.reply || data.message || data.response || '抱歉，我没有理解您的问题。'
    },

    // 获取会话ID
    getSessionId() {
      if (!this.sessionId) {
        this.sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
      }
      return this.sessionId
    },

    // 滚动到底部
    scrollToBottom() {
      if (this.$refs.chatMessages) {
        this.$refs.chatMessages.scrollTop = this.$refs.chatMessages.scrollHeight
      }
    },
    loadData(arg) {
      if (arg === 1) {
        this.ipagination.current = 1
      }

      const params = {
        pageNo: this.ipagination.current,
        pageSize: this.ipagination.pageSize,
        ...this.queryParam
      }
      
      this.loading = true
      getAction('/biz/userHelp/list', params).then(res => {
        if (res.success) {
          this.helpList = res.result.records || []
          this.ipagination.total = res.result.total || 0
        }
        this.loading = false
      }).catch(err => {
        this.loading = false
        this.$message.error('获取数据失败')
        console.error(err)
      })
    },
    // 分页
    handleChange(page) {
      this.ipagination.current = page
      this.loadData()
    },
    handleShowSizeChange(current, size) {
      this.ipagination.current = current
      this.ipagination.pageSize = size
      this.loadData()
    },

    // 获取关联文档
    getRelatedDocs(item) {
      if (!item.relatedDocs) {
        return []
      }
      
      try {
        const docs = JSON.parse(item.relatedDocs)
        return docs.sort((a, b) => a.order - b.order)
      } catch (e) {
        console.error('解析关联文档失败', e)
        return []
      }
    },
    // 点击视频播放
    handleVideoClick(item) {
      this.currentHelp = item
      this.videoVisible = true
    },
    // 关闭视频
    closeVideo() {
      this.videoVisible = false
      this.currentHelp = {}
    },
    // 打开关联文档
    openRelatedDoc(url) {
      if (url) {
        window.open(url, '_blank')
      } else {
        this.$message.warning('链接无效')
      }
    },
    // 初始化智能客服
    initChatbot() {
      // 动态加载智能客服CSS
      const chatbotCSS = document.createElement('link')
      chatbotCSS.rel = 'stylesheet'
      chatbotCSS.crossOrigin = 'anonymous'
      chatbotCSS.href = 'https://g.alicdn.com/aliyun-documentation/web-chatbot-ui/0.0.24/index.css'
      document.head.appendChild(chatbotCSS)

      // 动态加载智能客服JS
      const chatbotJS = document.createElement('script')
      chatbotJS.type = 'module'
      chatbotJS.crossOrigin = 'anonymous'
      chatbotJS.src = 'https://g.alicdn.com/aliyun-documentation/web-chatbot-ui/0.0.24/index.js'
      document.head.appendChild(chatbotJS)
    },

    // 初始化嵌入式聊天机器人
    initEmbeddedChatbot() {
      if (this.chatbotInitialized) return

      // 等待DOM更新后再初始化
      this.$nextTick(() => {
        // 配置智能客服
        window.CHATBOT_CONFIG = {
          endpoint: "https://webchat-bot-jm-mrhlyvmibi.cn-hangzhou.fcapp.run/chat",
          displayByDefault: true, // 直接显示
          title: '客服小九 - AI 助手',
          draggable: false, // 嵌入式不需要拖拽
          container: '#embedded-chatbot', // 指定容器
          aiChatOptions: {
            conversationOptions: {
              conversationStarters: this.quickQuestions.map(q => ({ prompt: q.prompt }))
            },
            displayOptions: {
              height: 450,
              width: '100%',
            },
            personaOptions: {
              assistant: {
                name: '客服小九',
                avatar: 'https://img.alicdn.com/imgextra/i2/O1CN01Pda9nq1YDV0mnZ31H_!!6000000003025-54-tps-120-120.apng',
                tagline: '我是您的专属智能助手，有什么问题都可以问我哦～',
              }
            },
            messageOptions: {
              waitTimeBeforeStreamCompletion: 'never'
            }
          },
          dataProcessor: {
            rewritePrompt(prompt) {
              return prompt;
            }
          }
        };

        // 触发聊天机器人初始化
        if (window.initWebchat) {
          window.initWebchat();
        }
      });

      this.chatbotInitialized = true
    }
  }
}
</script>

<style lang="less" scoped>
.modern-help-center {
  min-height: 100vh;
  background: #f5f7fa;
}

// 顶部导航栏
.top-navigation {
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  .nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 32px;
    display: flex;
    align-items: center;
    height: 70px;
  }

  .nav-brand {
    display: flex;
    align-items: center;
    gap: 16px;

    .brand-icon {
      width: 40px;
      height: 40px;
      background: #1890ff;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;

      .anticon {
        font-size: 20px;
        color: white;
      }
    }

    .brand-title {
      font-size: 24px;
      font-weight: 600;
      color: #262626;
      margin: 0;
    }
  }
}
// 主要内容区域
.main-content {
  position: relative;
  z-index: 5;
  padding: 32px;

  .content-container {
    max-width: 1400px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 400px 1fr;
    gap: 32px;
    align-items: start;
  }
}

// 智能客服区域
.chatbot-section {
  .chatbot-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8e8e8;
    overflow: hidden;
    position: sticky;
    top: 32px;
  }

  .chatbot-header {
    padding: 20px;
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;

      .avatar-container {
        position: relative;

        .chatbot-avatar {
          width: 56px;
          height: 56px;
          border-radius: 50%;
          border: 3px solid rgba(102, 126, 234, 0.2);
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .status-indicator {
          position: absolute;
          bottom: 2px;
          right: 2px;
          width: 16px;
          height: 16px;
          background: #52c41a;
          border: 3px solid white;
          border-radius: 50%;
          animation: pulse 2s infinite;
        }
      }

      .chatbot-info {
        .chatbot-name {
          font-size: 18px;
          font-weight: 600;
          color: #1f2937;
          margin: 0 0 4px 0;
        }

        .chatbot-status {
          font-size: 14px;
          color: #6b7280;
          margin: 0;
          display: flex;
          align-items: center;
          gap: 8px;

          &::before {
            content: '';
            width: 8px;
            height: 8px;
            background: #52c41a;
            border-radius: 50%;
            animation: blink 1.5s infinite;
          }
        }
      }
    }

    .header-actions {
      .chat-button {
        height: 36px;
        padding: 0 16px;
        background: #1890ff;
        border: none;
        border-radius: 6px;
        font-weight: 500;

        &:hover {
          background: #40a9ff;
        }
      }
    }
  }

  .chatbot-container {
    height: 350px;
    transition: all 0.3s ease;
    overflow: hidden;

    &.expanded {
      height: 500px;
    }

    .embedded-chatbot {
      width: 100%;
      height: 100%;
      border: none;
    }

    // 简单聊天界面
    .simple-chat {
      height: 100%;
      display: flex;
      flex-direction: column;
      padding: 16px;

      .chat-messages {
        flex: 1;
        overflow-y: auto;
        margin-bottom: 16px;
        padding-right: 8px;

        .message {
          margin-bottom: 12px;

          &.user {
            text-align: right;

            .message-content {
              display: inline-block;
              background: #1890ff;
              color: white;
              padding: 8px 12px;
              border-radius: 12px 12px 4px 12px;
              max-width: 70%;
              word-wrap: break-word;
            }
          }

          &.bot {
            text-align: left;

            .message-content {
              display: inline-block;
              background: #f0f0f0;
              color: #333;
              padding: 8px 12px;
              border-radius: 12px 12px 12px 4px;
              max-width: 70%;
              word-wrap: break-word;

              &.loading {
                background: #e6f7ff;
                color: #1890ff;
                font-style: italic;
              }
            }
          }
        }
      }

      .chat-input-area {
        display: flex;
        gap: 8px;

        .chat-input {
          flex: 1;
        }

        .send-button {
          width: 40px;
          height: 32px;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .chatbot-preview {
      padding: 24px;
      height: 100%;
      display: flex;
      flex-direction: column;

      .preview-content {
        flex: 1;

        .quick-questions {
          h4 {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;

            &::before {
              content: '💡';
              font-size: 18px;
            }
          }

          .question-list {
            display: flex;
            flex-direction: column;
            gap: 12px;

            .question-item {
              padding: 12px;
              background: #f5f5f5;
              border: 1px solid #d9d9d9;
              border-radius: 6px;
              cursor: pointer;
              transition: all 0.2s ease;
              display: flex;
              align-items: center;
              gap: 8px;

              &:hover {
                background: #e6f7ff;
                border-color: #1890ff;
              }

              .anticon {
                font-size: 14px;
                color: #1890ff;
              }

              span {
                font-size: 13px;
                color: #595959;
              }
            }
          }
        }
      }
    }
  }
}
// 视频区域
.videos-section {
  .section-header {
    margin-bottom: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e8e8e8;

    .section-title {
      font-size: 20px;
      font-weight: 600;
      color: #262626;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 8px;

      .anticon {
        font-size: 20px;
        color: #1890ff;
      }
    }
  }

  .videos-loading {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e8e8e8;
    min-height: 400px;

    /deep/ .ant-spin-container {
      padding: 20px;
    }
  }

  .videos-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;

    .video-card {
      background: white;
      border-radius: 8px;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.2s ease;
      border: 1px solid #e8e8e8;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
        border-color: #1890ff;

        .play-overlay {
          opacity: 1;
        }
      }

      .video-thumbnail {
        position: relative;
        padding-top: 56.25%; /* 16:9 比例 */
        overflow: hidden;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

        .thumbnail-img {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.3s ease;
        }

        .default-thumbnail {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;

          .anticon {
            font-size: 48px;
            color: #cbd5e1;
          }
        }

        .play-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.4);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: all 0.2s ease;

          .play-icon {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;

            .anticon {
              font-size: 24px;
              color: #1890ff;
              margin-left: 2px;
            }
          }
        }

        .video-duration {
          position: absolute;
          bottom: 8px;
          right: 8px;
          background: rgba(0, 0, 0, 0.8);
          color: white;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 600;
        }
      }

      .video-info {
        padding: 20px;

        .video-title {
          font-size: 16px;
          font-weight: 600;
          color: #1f2937;
          margin: 0 0 8px 0;
          line-height: 1.4;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }

        .video-description {
          font-size: 14px;
          color: #6b7280;
          margin: 0 0 16px 0;
          line-height: 1.5;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }

        .related-docs {
          .docs-label {
            font-size: 12px;
            color: #9ca3af;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 4px;

            .anticon {
              font-size: 12px;
            }
          }

          .docs-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;

            .doc-tag {
              font-size: 12px;
              padding: 2px 6px;
              background: #f0f0f0;
              border: 1px solid #d9d9d9;
              color: #595959;
              border-radius: 4px;
              cursor: pointer;
              transition: all 0.2s ease;

              &:hover {
                background: #e6f7ff;
                border-color: #1890ff;
                color: #1890ff;
              }
            }
          }
        }
      }
    }
  }

  .empty-state {
    padding: 64px 24px;

    /deep/ .ant-empty-description {
      color: #9ca3af;
      font-size: 16px;
    }
  }

  .pagination-wrapper {
    margin-top: 32px;
    padding: 24px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-align: center;

    .modern-pagination {
      /deep/ .ant-pagination-item {
        border-radius: 8px;
        border: 1px solid rgba(102, 126, 234, 0.2);

        &:hover {
          border-color: #667eea;
        }

        &.ant-pagination-item-active {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-color: #667eea;

          a {
            color: white;
          }
        }
      }

      /deep/ .ant-pagination-prev, /deep/ .ant-pagination-next {
        border-radius: 8px;
        border: 1px solid rgba(102, 126, 234, 0.2);

        &:hover {
          border-color: #667eea;
          color: #667eea;
        }
      }
    }
  }
}
// 视频弹窗样式
.modern-video-modal {
  /deep/ .ant-modal {
    max-width: 90vw;

    .ant-modal-content {
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 24px 64px rgba(0, 0, 0, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .ant-modal-header {
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding: 20px 24px;

      .ant-modal-title {
        font-size: 18px;
        font-weight: 600;
        color: #1f2937;
      }
    }

    .ant-modal-body {
      padding: 0;
      background: #000;
    }

    .ant-modal-close {
      top: 16px;
      right: 16px;

      .ant-modal-close-x {
        width: 40px;
        height: 40px;
        line-height: 40px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: scale(1.1);
        }

        .anticon {
          color: white;
          font-size: 16px;
        }
      }
    }
  }

  .modal-video-container {
    width: 100%;
    background: #000;
    border-radius: 0 0 16px 16px;
    overflow: hidden;

    .modal-video-wrapper {
      position: relative;
      width: 100%;
      height: 0;
      padding-bottom: 56.25%; /* 16:9 比例 */
    }

    .modal-video-player {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
}

// 动画效果
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  25%, 75% {
    opacity: 0.5;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .main-content {
    .content-container {
      grid-template-columns: 1fr;
      gap: 24px;
    }
  }

  .chatbot-section {
    .chatbot-card {
      position: relative;
      top: auto;
    }
  }

  .videos-section {
    .videos-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
    }
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 16px;
  }

  .top-navigation {
    .nav-container {
      padding: 0 16px;
      height: 70px;
      flex-direction: column;
      gap: 16px;

      &.mobile-nav {
        height: auto;
        padding: 16px;
      }
    }

    .nav-brand {
      .brand-icon {
        width: 40px;
        height: 40px;

        .anticon {
          font-size: 20px;
        }
      }

      .brand-title {
        font-size: 24px;
      }
    }

    .nav-actions {
      .search-input {
        width: 100%;
        max-width: 300px;
      }
    }
  }

  .videos-section {
    .section-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .section-title {
        font-size: 20px;
      }
    }

    .videos-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }

  .chatbot-section {
    .chatbot-header {
      padding: 16px;

      .header-content {
        gap: 12px;

        .avatar-container .chatbot-avatar {
          width: 48px;
          height: 48px;
        }

        .chatbot-info {
          .chatbot-name {
            font-size: 16px;
          }

          .chatbot-status {
            font-size: 13px;
          }
        }
      }

      .header-actions {
        .chat-button {
          height: 36px;
          padding: 0 16px;
          font-size: 14px;
        }
      }
    }

    .chatbot-container {
      height: 350px;

      &.expanded {
        height: 500px;
      }

      .chatbot-preview {
        padding: 16px;

        .quick-questions {
          .question-list {
            gap: 10px;

            .question-item {
              padding: 12px;

              span {
                font-size: 13px;
              }
            }
          }
        }
      }
    }
  }
}


/* 智能客服嵌入式样式优化 */
/deep/ :root {
  --webchat-toolbar-background-color: #667eea;
  --webchat-toolbar-text-color: #FFF;
}

/* 嵌入式聊天机器人样式 */
/deep/ #embedded-chatbot {
  .webchat-container {
    position: relative !important;
    transform: none !important;
    top: auto !important;
    left: auto !important;
    width: 100% !important;
    height: 100% !important;
    max-width: none !important;
    max-height: none !important;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: none;
    border: 1px solid rgba(102, 126, 234, 0.1);
  }

  .webchat-bubble-tip {
    display: none !important;
  }
}

/* 如果还有浮动的客服按钮，隐藏它 */
/deep/ .webchat-bubble-tip {
  display: none !important;
}
</style> 