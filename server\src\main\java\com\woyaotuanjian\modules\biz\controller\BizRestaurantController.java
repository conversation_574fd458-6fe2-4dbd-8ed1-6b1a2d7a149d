package com.woyaotuanjian.modules.biz.controller;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.woyaotuanjian.common.api.vo.Result;
import com.woyaotuanjian.common.aspect.annotation.AutoLog;
import com.woyaotuanjian.common.constant.RoleConstant;
import com.woyaotuanjian.common.system.vo.LoginUser;
import com.woyaotuanjian.modules.biz.entity.BizRestaurant;
import com.woyaotuanjian.modules.biz.entity.pojo.BizRestaurantExt;
import com.woyaotuanjian.modules.biz.entity.pojo.RefreshCmd;
import com.woyaotuanjian.modules.biz.mapper.BizRestaurantMapper;
import com.woyaotuanjian.modules.biz.service.IBizCompanyService;
import com.woyaotuanjian.modules.biz.service.IBizRestaurantService;
import com.woyaotuanjian.modules.biz.service.IBizTripService;
import com.woyaotuanjian.modules.biz.util.ExpressionParserUtil;
import com.woyaotuanjian.modules.biz.util.SysUserUtil;
import com.woyaotuanjian.modules.biz.util.YdUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @Description: 餐厅
 * @Author: jeecg-boot
 * @Date:   2021-06-24
 * @Version: V1.0
 */
@Slf4j
@Api(tags="餐厅")
@RestController
@RequestMapping("/biz/bizRestaurant")
public class BizRestaurantController {
	@Autowired
	private IBizRestaurantService bizRestaurantService;
	@Resource
	private BizRestaurantMapper restaurantMapper;
	@Autowired
	private IBizTripService tripService;
	@Autowired
	private IBizCompanyService companyService;
	/**
	  * 分页列表查询
	 * @param bizRestaurantExt
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "餐厅-分页列表查询")
	@ApiOperation(value="餐厅-分页列表查询", notes="餐厅-分页列表查询")
	@GetMapping(value = "/list")
	public Result queryPageList(BizRestaurantExt bizRestaurantExt,
									  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
									  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								      @RequestParam(name="onlyCheckedCom", defaultValue="1") Integer onlyCheckedCom,
								HttpServletRequest req) {
		Result result = new Result();

		Map<String,Object> param=YdUtil.sqlMap(req.getParameterMap());
		LoginUser sysUser=SysUserUtil.getCurrentUser();
		//根据登陆人过滤数据
		companyService.dataListFilter(param,sysUser,onlyCheckedCom);

		IPage<BizRestaurantExt> pageList = bizRestaurantService.getBizRestaurantExtList(new Page(pageNo, pageSize),param);
		result.setSuccess(true);
		result.setResult(pageList);
		return result;
	}
	
	/**
	  *   添加
	 * @param bizRestaurant
	 * @return
	 */
	@AutoLog(value = "餐厅-添加")
	@ApiOperation(value="餐厅-添加", notes="餐厅-添加")
	@PostMapping(value = "/add")
	@RequiresRoles(value = {RoleConstant.ADMIN,RoleConstant.B1,RoleConstant.B2SUPER},logical = Logical.OR)
	public Result<BizRestaurant> add(@RequestBody BizRestaurantExt bizRestaurant) {
		Result result = new Result();
		try {
			// 检查餐厅名称是否重复
			QueryWrapper<BizRestaurant> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("rest_name", bizRestaurant.getRestName());
			// 如果是B2用户，只检查自己创建的数据
			LoginUser loginUser = SysUserUtil.getCurrentUser();
			if(loginUser.getRoleCode().startsWith(RoleConstant.B2)) {
				queryWrapper.eq("sys_user_id", loginUser.getId());
			} else {
				// 如果是其他用户，检查同公司下的数据
				queryWrapper.eq("com_id", loginUser.getComId());
			}
			int count = bizRestaurantService.count(queryWrapper);
			if(count > 0) {
				result.error500("餐厅名称已存在！");
				return result;
			}

			Date now = new Date();
			bizRestaurant.setVersion(0);
			if(YdUtil.empty(bizRestaurant.getRegionProvince())){
				bizRestaurant.setRegionProvince("全国");
			}
			if(YdUtil.empty(bizRestaurant.getRegionCity())){
				bizRestaurant.setRegionCity("全国");
			}
			bizRestaurant.setCreateTime(now);
			bizRestaurant.setUpdateTime(now);
			bizRestaurant.setSysUserId(loginUser.getId());
			if(loginUser.getRoleCode().equals(RoleConstant.ADMIN)){//管理员
				bizRestaurant.setComId(loginUser.getComId());
			}else if(loginUser.getRoleCode().startsWith(RoleConstant.B1)){//b1或者b1super
				bizRestaurant.setComId(loginUser.getComId());
			}else if(loginUser.getRoleCode().startsWith(RoleConstant.B2)) {//b2或者b2super
				//私有数据，不添加com
			}
			bizRestaurantService.save(bizRestaurant);
			result.success("添加成功！");
			result.setResult(bizRestaurant);
		} catch (Exception e) {
			log.error(e.getMessage(),e);
			result.error500("操作失败");
		}
		return result;
	}
	
	/**
	  *  编辑
	 * @param bizRestaurant
	 * @return
	 */
	@AutoLog(value = "餐厅-编辑")
	@ApiOperation(value="餐厅-编辑", notes="餐厅-编辑")
	@PutMapping(value = "/edit")
	public Result edit(@RequestBody BizRestaurantExt bizRestaurant) {
		Result result = new Result<>();
		BizRestaurant bizRestaurantEntity = bizRestaurantService.getById(bizRestaurant.getId());
		if(bizRestaurantEntity==null) {
			result.error500("未找到对应实体");
		}else {

			LoginUser loginUser=SysUserUtil.getCurrentUser();
			//检查编辑和删除权限
			Result check=companyService.checkBasicDataPermission(loginUser,bizRestaurantEntity.getComId(),bizRestaurantEntity.getSysUserId());
			if(!check.isSuccess()){
				return check;
			}

			bizRestaurant.setUpdateTime(new Date());
			boolean ok = bizRestaurantService.updateById(bizRestaurant);
			Set<String> cmd=new HashSet<>();
			cmd.add(RefreshCmd.DEFAULT);
			if(bizRestaurant.getAutoFillingDesc()){
				cmd.add(RefreshCmd.REFRESH_RICHTEXT);
			}
			tripService.refreshTrip("rest",bizRestaurant.getId(),cmd);
			result.success("修改成功!");
		}
		
		return result;
	}
	
	/**
	  *   通过id删除
	 * @param id
	 * @return
	 */
	@AutoLog(value = "餐厅-通过id删除")
	@ApiOperation(value="餐厅-通过id删除", notes="餐厅-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result delete(@RequestParam(name="id",required=true) String id) {
		Result<BizRestaurant> result = new Result<BizRestaurant>();
		BizRestaurant bizRestaurant = bizRestaurantService.getById(id);
		if(bizRestaurant==null) {
			result.error500("未找到对应实体");
		}else {

			LoginUser loginUser=SysUserUtil.getCurrentUser();

			//检查编辑和删除权限
			Result check=companyService.checkBasicDataPermission(loginUser,bizRestaurant.getComId(),bizRestaurant.getSysUserId());
			if(!check.isSuccess()){
				return check;
			}

			List<Long> associatedTripIDList = tripService.getAssociatedTripIdList("rest",bizRestaurant.getId());
			if (associatedTripIDList.size()>0){
				String retStr = "行程已使用,不允许删除. 数量：" + associatedTripIDList.size()+",详细："
						+ associatedTripIDList.stream().limit(10).collect(Collectors.toList()).toString();
				if (associatedTripIDList.size()>10){
					retStr = retStr + "……";
				}
				return Result.error(retStr);
			}

			boolean ok = bizRestaurantService.removeById(id);
			if(ok) {
				result.success("删除成功!");
			}
		}
		
		return result;
	}

	
	/**
	  * 通过id查询
	 * @param id
	 * @return
	 */
	@AutoLog(value = "餐厅-通过id查询")
	@ApiOperation(value="餐厅-通过id查询", notes="餐厅-通过id查询")
	@GetMapping(value = "/queryById")
	public Result queryById(@RequestParam(name="id",required=true) String id) {
		Result result = new Result<>();
		BizRestaurant bizRestaurant = bizRestaurantService.getById(id);
		JSONObject resJson = (JSONObject) JSONObject.toJSON(bizRestaurant);
		resJson.put("restTag", JSONArray.parse(bizRestaurant.getRestTag()));
		if(bizRestaurant==null) {
			result.error500("未找到对应实体");
		}else {
			result.setResult(resJson);
			result.setSuccess(true);
		}
		return result;
	}

	/**
	 * 面板查询
	 * @param req
	 * @return
	 */
	@ApiOperation(value="餐厅-面板查询", notes="餐厅-面板查询")
	@GetMapping(value = "/restDataList")
	public Result restDataList(HttpServletRequest req,@RequestParam(value = "search",required = false)String search) {
		HashMap<String,Object> param=YdUtil.sqlMap(req.getParameterMap());
		if(search!=null&&search.equals("null")){
			param.remove("search");
		}
		
		// 处理地区筛选参数
		List<Map<String, String>> regionFilters = new ArrayList<>();
		Map<String, String[]> parameterMap = req.getParameterMap();
		
		// 提取地区筛选参数
		for (String key : parameterMap.keySet()) {
			if (key.startsWith("regionProvince") || key.startsWith("regionCity") || key.startsWith("regionArea")) {
				String indexStr = key.replaceAll("[^0-9]", ""); // 提取数字索引
				if (!indexStr.isEmpty()) {
					int index = Integer.parseInt(indexStr);
					
					// 确保regionFilters有足够的元素
					while (regionFilters.size() <= index) {
						regionFilters.add(new HashMap<>());
					}
					
					String value = parameterMap.get(key)[0];
					if (key.startsWith("regionProvince")) {
						regionFilters.get(index).put("province", value);
					} else if (key.startsWith("regionCity")) {
						regionFilters.get(index).put("city", value);
					} else if (key.startsWith("regionArea")) {
						regionFilters.get(index).put("area", value);
					}
				}
			}
		}
		
		// 将地区筛选参数添加到param中
		if (!regionFilters.isEmpty()) {
			param.put("regionFilters", regionFilters);
		}
		
		LoginUser sysUser=SysUserUtil.getCurrentUser();
		//根据登陆人过滤数据
		companyService.panelDataListFilter(param,sysUser);
		//我的数据优先
		param.put("currentUserId",sysUser.getId());

		List<BizRestaurantExt> list = restaurantMapper.getRestPanelList(param);
		list.stream().forEach(item->{
			if(YdUtil.empty(item.getRegionCity())){
				item.setRegionCity("全国");
			}
			item.setRestDesc(ExpressionParserUtil.filterExpression(item.getRestDesc()));
		});
		//全部城市名称
		List<String> allCityList=list.stream().map(BizRestaurant::getRegionCity).distinct().collect(Collectors.toList());
		allCityList.add(0,"全部");

		//所有景区名称按城市分组
		Map<String,List<String>> cityMap=new ConcurrentHashMap<>();
		//cityMap.put("全部",list.stream().map(BizRestaurant::getRestName).collect(Collectors.toList()));
		List<String> allRestNameList = new ArrayList<>(100);

		//所有数据改为Map<name,data>，方便查找
		Map<String,BizRestaurantExt> nameMap=new ConcurrentHashMap<>();
		list.stream().forEach(item->{
			String name=item.getRestName();
			//不同用户可能创建相同的名称，如有相同，则加 @用户名 的后缀，方便识别
			if (allRestNameList.contains(name)){
				name = name +"@" +item.getSysUserName();
			}
			allRestNameList.add(name);
			nameMap.put(name,item);

			List<String> nameList=cityMap.get(item.getRegionCity());
			if(nameList==null){
				nameList=new ArrayList<>(10);
			}
			nameList.add(name);
			cityMap.put(item.getRegionCity(),nameList);
		});
		cityMap.put("全部",allRestNameList);

		JSONObject resp=new JSONObject();
		resp.put("cityList",allCityList);
		resp.put("cityMap",cityMap);
		resp.put("nameMap",nameMap);
		return Result.ok(resp);
	}

	/**
	 * 批量获取餐厅坐标
	 * @param ids 餐厅ID列表，逗号分隔
	 * @return 餐厅坐标列表
	 */
	@AutoLog(value = "餐厅-批量获取坐标")
	@ApiOperation(value="餐厅-批量获取坐标", notes="餐厅-批量获取坐标")
	@GetMapping(value = "/coordinates")
	public Result getCoordinates(@RequestParam(name="ids",required=true) String ids) {
		Result result = new Result<>();
		try {
			String[] idArray = ids.split(",");
			List<Map<String, Object>> coordinates = new ArrayList<>();
			
			for (String id : idArray) {
				if (id != null && !id.trim().isEmpty()) {
					BizRestaurant restaurant = bizRestaurantService.getById(id.trim());
					if (restaurant != null && restaurant.getLongitude() != null && restaurant.getLatitude() != null) {
						Map<String, Object> coord = new HashMap<>();
						coord.put("id", restaurant.getId());
						coord.put("name", restaurant.getRestName());
						coord.put("longitude", restaurant.getLongitude());
						coord.put("latitude", restaurant.getLatitude());
						coord.put("type", "restaurant");
						coordinates.add(coord);
					}
				}
			}
			
			result.setSuccess(true);
			result.setResult(coordinates);
		} catch (Exception e) {
			log.error("获取餐厅坐标失败:", e);
			result.error500("获取坐标失败:" + e.getMessage());
		}
		return result;
	}

}
