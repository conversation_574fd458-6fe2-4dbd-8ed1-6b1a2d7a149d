<template>
  <div>
    <a-spin :spinning="loading">
      <a-card :bordered="false">
        <a-form layout="horizontal" class="form">
          <a-form-item label="景区名称" class="my-form-item">
            <a-input v-model="model.scenicName" placeholder="景区名称" style='width: 350px;' />
            <a-input v-model="model.scenicSimpleName" placeholder="简称" style='width: 110px;margin-left: 10px;' />
            <a-tooltip title="输入景区链接，可自动填空">
              <a-icon type="robot" style="margin-left: 10px; color: aqua;" @click="openUrlModal" />
            </a-tooltip>
          </a-form-item>
          <a-form-item label="特色简述" class="my-form-item">
            <a-input placeholder="一句话描述景点特色" v-model='model.advantageDesc' />
          </a-form-item>
          <a-form-item label="景区介绍" class="my-form-item">
            <a-textarea placeholder="用于景区图文详细介绍素材，支持 # 插入图片" class="textarea" :autoSize="{ minRows: 6, maxRows: 30 }"
              v-model='model.scenicDesc'></a-textarea>
          </a-form-item>
          <a-form-item label="行程安排" class="my-form-item">
            <a-textarea placeholder="用于行程时间表展现，支持 * 分割多个子行程" class="textarea" :autoSize="{ minRows: 3, maxRows: 30 }"
              v-model='model.tripDesc'></a-textarea>
          </a-form-item>
          <a-form-item label="地理位置" class="my-form-item">
            <a-cascader popupClassName="media-edit-cascader" change-on-select :options="areaData" v-model="area"
              :showSearch="true" @change="onAreaChange" placeholder="请选择" />
          </a-form-item>
          <!-- <a-form-item label="详细地址" class="my-form-item">
            <a-input v-model="model.regionAddress" placeholder="请输入详细地址" />
          </a-form-item> -->
          <a-form-item label="地理坐标" class="my-form-item">
            <a-input-number placeholder="经度" v-model="model.longitude" style="width: 120px;" :precision="6" />
            <span style="margin: 0 10px;">，</span>
            <a-input-number placeholder="纬度" v-model="model.latitude" style="width: 120px;" :precision="6" />
                      <a-button type="primary" style="margin-left: 10px;" @click="openMapPicker">
            <a-icon type="environment" />地图选点
          </a-button>
          </a-form-item>
          <!-- <a-form-item label="景区等级" class="my-form-item">
            <a-select v-model="model.scenicLevel" placeholder="请选择景区等级" style="width: 150px;" allowClear>
              <a-select-option v-for="item in scenicLevelOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>  -->
         
          <a-form-item label="特色标签" class="my-form-item checkbox-area">
            <a-checkbox-group v-model="model.scenicTag">
              <a-row>
                <a-col v-for="(item, index) in this.scenicTagList" :key="index" :span="6">
                  <a-checkbox :name="item.itemText" :value="item.itemText">{{ item.itemText }}</a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
          </a-form-item>
          <a-form-item label="适合人群" class="my-form-item checkbox-area">
            <a-checkbox-group v-model="model.scenicUserTag">
              <div style="display: inline-block; margin-right: 8px;" v-for="(item, index) in this.scenicUserTagList"
                :key="`适合人群${index}`">
                <a-checkbox :name="item.itemText" :value="item.itemText">{{ item.itemText }}</a-checkbox>
              </div>
            </a-checkbox-group>
          </a-form-item>

          <!-- 门票成本和成本备注区域 -->
          <div class="form-group" v-if="this.isInputCost">
            <a-form-item label="门票成本" class="my-form-item">
              <a-input-number placeholder="门票成本" v-model="model.cost" style="width: 100px;" />
            </a-form-item>
            <a-form-item label="成本备注" class="my-form-item">
              <a-textarea placeholder="成本备注，供定制师参考" class="textarea" :auto-size="{ minRows: 3, maxRows: 30 }"
                v-model="model.costRemark"></a-textarea>
            </a-form-item>
          </div>

          <a-form-item label="门票费用" class="my-form-item">
            <span>默认 </span>
            <a-input-number :default-value="0" placeholder="0" v-model="model.price" style="width: 100px;" />
            <button type="button" class="ant-btn ant-btn-primary" style="margin-left: 10px;"
              @click="openPriceModal">设置价格</button>
            <button type="button" class="ant-btn ant-btn-primary" style="margin-left: 10px;"
              @click="openFullMinusModal">设置满减</button>
          </a-form-item>
          <a-form-item label="费用备注" class="my-form-item">
            <a-textarea placeholder="景区费用说明, 小孩费用、小景点费用等. 用于word/excel导出报价部分" class="textarea"
              :auto-size="{ minRows: 3, maxRows: 30 }" v-model="model.priceRemark"></a-textarea>
          </a-form-item>
          <!-- <a-form-item label="浏览时长" class="my-form-item">
            <a-input-number placeholder="小时" v-model="model.costTime" />
            <span>小时</span>
          </a-form-item> -->
          <a-form-item label="介绍图" class="my-form-item-upload">
            <div class="clearfix">
              <upload-image :key="'scenic_' + model.id" @getImageId="getImageId" @delImageId="delImageId" :multiple="30"
                :img="upload.fileList" @draggable="handleDraggable" v-if="loadFinished"></upload-image>
            </div>
          </a-form-item>
          <a-form-item label="封面图" class="my-form-item-upload">
            <div class="clearfix">
              <upload-image :key="'scenic_cover_' + model.id" @getImageId="getImageIdCover"
                @delImageId="delImageIdCover" :multiple="2" :max-file-size="5" :img="upload.coverFileList"
                @draggable="handleDraggableCover" v-if="loadFinished"></upload-image>
            </div>
          </a-form-item>
          <a-form-item label="附件" class="my-form-item-upload">
            <div class="attach-box">
              <a-upload name="file" :action="uploadUrl" :fileList="fileUpload.fileList"
                :beforeUpload="beforeFileUpload" @change="handleFileChange"
                :showUploadList="{ showRemoveIcon: true, showPreviewIcon: true }">
                <a-button>
                  <a-icon type="upload" />
                  上传附件
                </a-button>
              </a-upload>
            </div>
          </a-form-item>
          <a-form-item label="搜索指数" class="my-form-item">
            <a-input-number style="width: 150px;" placeholder="大于0为热门景点" v-model="model.searchIndex" />
          </a-form-item>
          <a-form-item label="状态" class="my-form-item">
            <j-dict-select-tag style="width: 100px;" v-model="model.status" placeholder="请选择"
              dictCode="biz_scenic_status" />
          </a-form-item>
          <a-form-item class="my-center">
            <button type="button" class="ant-btn ant-btn-primary" @click="save"><span>确 定</span></button>
            <button type="button" class="ant-btn" style="margin-left: 10px;" @click="this.closeCurrent"><span>关
                闭</span></button>
          </a-form-item>
        </a-form>
        <set-price-modal ref="priceModal" @afterSetPrice="afterSetPrice"></set-price-modal>
        <set-full-minus ref="fullMinusModal" @afterSetFullMinus="afterSetFullMinus"></set-full-minus>
      </a-card>
    </a-spin>
    <!-- 采集网络链接弹窗 -->
    <a-modal :visible="urlModalVisible" title="输入景区链接" @ok="validateAndFetchUrl" @cancel="closeUrlModal">
      <a-form-item label="网址">
        <a-input v-model="inputUrl" placeholder="请输入景区链接" />
      </a-form-item>
      <span v-if="urlError" style="color:red">{{ urlError }}</span>
      <div class="hint">
        <a-icon type="info-circle" style="color:orange;" />
        <span
          style="margin-left: 8px;">温馨提示：目前仅支持携程景区链接<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;例如：https://you.ctrip.com/sight/beijing1/228.html</span>
      </div>
      <a-progress :percent="collectProgress" v-if="collectLoading" />

      <template slot="footer">
        <a-button key="back" @click="closeUrlModal">取消</a-button>
        <a-button key="submit" type="primary" @click="validateAndFetchUrl" :loading="collectLoading">
          确定
        </a-button>
      </template>
    </a-modal>
    <!-- 地图选点组件 -->
    <map-picker 
      ref="mapPicker"
      :initial-longitude="model.longitude"
      :initial-latitude="model.latitude"
      :initial-keyword="model.scenicName"
      search-region="全国"
      search-category="风景名胜,旅游景点,博物馆,公园,文物古迹"
      @select="onMapSelect"
      @address-info="onAddressInfo"
    />
  </div>
</template>

<script>
import { deleteAction, getAction, postAction, putAction } from '@/api/manage';
import { RouterLinkMixinConfig } from '@/mixins/RouterLinkMixinConfig';
import ChineseDistricts from '@/assets/distpicker.data.min.js';
import { axios } from '@/utils/request';
import SetPriceModal from '@comp/priceModal/SetPriceModal.vue';
import UploadImage from '@comp/UploadImage/UploadImage';
import SetFullMinus from './SetFullMinus';
import MapPicker from '@/components/MapPicker/MapPicker.vue';

// 处理地区数组
function getCityArr(cityJson) {
  let Pobj = cityJson[1];
  let Pvalue = Object.keys(Pobj);

  let option = Pvalue.map((item) => {
    let arr = { value: Pobj[item], label: Pobj[item] };
    if (Object.keys(cityJson).includes(item)) {
      arr.children = getCityArrChild(cityJson, item);
    }
    return arr;
  });
  return option;
}
function getCityArrChild(cityJson, key) {
  let Cobj = cityJson[key];
  let Cvalue = Object.keys(Cobj);

  let Coption = Cvalue.map((item) => {
    let Carr = { value: Cobj[item], label: Cobj[item] };
    if (Object.keys(cityJson).includes(item)) {
      Carr.children = getCityArrChild(cityJson, item);
    }
    return Carr;
  });
  return Coption;
}

export default {
  name: 'BizScenicEdit',
  mixins: [RouterLinkMixinConfig],
  inject: ['closeCurrent', 'onCloseCurrent'],
  components: {
    ChineseDistricts,
    SetPriceModal,
    UploadImage,
    SetFullMinus,
    MapPicker,
  },
  activated() {
    this.loading = false;
    this.fetchPageData();
    if (this.$route.params.id) {
      this.model.id = this.$route.params.id;
      this.getEditData();
    } else {
      this.loadFinished = true;
    }
  },
  data() {
    return {
      model: {
        id: null,
        tripDesc: null,
        advantageDesc: null,
        scenicDesc: null,
        price: null,
        scenicName: null,
        scenicSimpleName: null,
        costTime: 0,
        searchIndex: 0,
        status: "1",
        priceRemark: null,
        scenicTag: [],
        scenicUserTag: [],
        imgUrl: null,
        coverImgUrl: null,
        attach: null,
        thirdUrl: null,
        fullMinus: null,
        minPcs: 0,
        isInputCost: false,
        costRemark: null,
        cost: null,
        longitude: null,
        latitude: null,
        regionAddress: null,
        scenicLevel: null
      },
      resetModel: {
        id: null,
        tripDesc: null,
        advantageDesc: null,
        scenicDesc: null,
        price: null,
        scenicName: null,
        scenicSimpleName: null,
        costTime: 0,
        searchIndex: 0,
        status: "1",
        priceRemark: null,
        scenicTag: [],
        scenicUserTag: [],
        imgUrl: null,
        coverImgUrl: null,
        attach: null,
        thirdUrl: null,
        fullMinus: null,
        minPcs: 0,
        isInputCost: false,
        costRemark: null,
        cost: null,
        longitude: null,
        latitude: null,
        regionAddress: null,
        scenicLevel: null
      },
      upload: {
        uploadUrl: "/file/upload",
        fileList: [],
        coverFileList: []
      },
      //附件
      fileUpload: {
        fileList: [],
      },
      // 景区标签
      scenicTagList: [],
      // 景区用户标签
      scenicUserTagList: [],
      // 景区等级选项
      scenicLevelOptions: [
        { value: 0, label: '无' },
        { value: 1, label: '1A' },
        { value: 2, label: '2A' },
        { value: 3, label: '3A' },
        { value: 4, label: '4A' },
        { value: 5, label: '5A' }
      ],
      // 全部区域数据
      areaData: [],
      // 选择出来的区域数组，省-市-县
      area: [],
      loading: true,
      loadFinished: false,
      isInputCost: false,
      url: {
        add: "/biz/bizScenic/add",
        get: "/biz/bizScenic/queryById",
        edit: "/biz/bizScenic/edit",
        getCom: "/biz/bizCompany/queryById"
      },
      urlModalVisible: false,
      inputUrl: '',
      urlError: '',
      collectLoading: false,
      collectProgress: 0,
      collectInterval: null,
    };
  },
  computed: {
    uploadUrl() {
      let url = (axios.defaults.baseURL || "") + '/file/upload';
      return url;
    }
  },
  mounted() {
    this.sysUser = this.$store.getters.userInfo;
    // 如果comID为0或者null，则不获取com信息
    if (this.sysUser.comId == 0 || this.sysUser.comId == null) {
      return;
    }
    // 根据this.sysUser.comID获取com信息
    getAction(this.url.getCom, { id: this.sysUser.comId })
      .then((res) => {
        if (res.success) {
          this.model.comName = res.result.comName;
          // 获取config中的enableInputCost
          if (res.result.config != null) {
            let config = JSON.parse(res.result.config);
            this.isInputCost = config.enableCostInput;
            // 如果sysUser.roleCode.startsWith('biz')不成立，则不显示成本
            if (!this.sysUser.roleCode.startsWith('biz')) {
              this.isInputCost = false;
            }
          }
        } else {
          this.$message.warning(res.message || '请求错误');
        }
      })
      .catch((res) => {
        this.$message.warning(res.message || '网络错误');
      });
  },
  created() {
    this.onCloseCurrent(this.$route.path, () => {
      this.reset();
      this.$router.push({ path: "/biz/bizScenic/list" });
    });
    this.areaData = getCityArr(ChineseDistricts);
  },
  methods: {
    afterSetPrice(priceRule) {
      // 空对象
      if (Object.keys(priceRule).length == 0) {
        this.model.priceRule = "";
      } else {
        this.model.priceRule = JSON.stringify(priceRule);
      }
    },
    afterSetFullMinus(fullMinus, minPcs) {
      this.model.fullMinus = fullMinus;
      this.model.minPcs = minPcs;
    },
    openPriceModal() {
      let priceRule = JSON.parse(this.model.priceRule || "{}");
      this.$refs.priceModal.initModal('scenic', null, priceRule);
      this.$refs.priceModal.title = '设置价格';
    },
    openFullMinusModal() {
      this.$refs.fullMinusModal.initModal('scenic', null, this.model.fullMinus, this.model.minPcs);
      this.$refs.fullMinusModal.title = '设置满减';
    },
    onAreaChange(value) {
      this.model.regionProvince = value[0];
      this.model.regionCity = value[1];
      this.model.regionArea = value[2];
      this.model.regionCountry = '中国';
      this.model.region = value.join("-");
    },
    getEditData() {
      // 加载编辑数据
      getAction(this.url.get, { id: this.$route.params.id })
        .then((res) => {
          if (res.success) {
            this.model = res.result;
            this.model.createTime = null;
            this.model.updateTime = null;
            this.area = [this.model.regionProvince, this.model.regionCity, this.model.regionArea];
            if (this.model.imgUrl != null && this.model.imgUrl.length > 0) {
              this.upload.fileList = this.model.imgUrl.split(",");
            }
            if (this.model.coverImgUrl != null && this.model.coverImgUrl.length > 0) {
              this.upload.coverFileList = this.model.coverImgUrl.split(",");
            }
            // 文件回显
            if (res.result.attach != null && res.result.attach.length > 0) {
              this.fileUpload.fileList = JSON.parse(res.result.attach);
            }
            this.loadFinished = true;
          } else {
            this.$message.warning(res.message || '请求错误');
          }
        })
        .catch((res) => {
          this.$message.warning(res.message || '网络错误');
        });
    },
    // 获取上传的图片
    getImageId(val) {
      this.upload.fileList = this.upload.fileList.concat(val);
    },
    // 删除图片
    delImageId(val) {
      this.upload.fileList = this.upload.fileList.filter((x) => x !== val);
    },
    // 获取到重新排序后的图片
    handleDraggable(e) {
      this.upload.fileList = e;
    },
    // 获取上传的封面图片
    getImageIdCover(val) {
      this.upload.coverFileList = this.upload.coverFileList.concat(val);
    },
    // 删除封面图片
    delImageIdCover(val) {
      this.upload.coverFileList = this.upload.coverFileList.filter((x) => x !== val);
    },
    // 获取到重新排序后的封面图片
    handleDraggableCover(e) {
      this.upload.coverFileList = e;
    },
    // 上传文件前的校验
    beforeFileUpload(file) {
      const MAX_FILE_SIZE_MB = 10; // 10MB 大小限制
      const VALID_TYPES = [
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ];

      const isSizeValid = file.size / 1024 / 1024 <= MAX_FILE_SIZE_MB;
      const isTypeValid = VALID_TYPES.includes(file.type);

      if (!isSizeValid) {
        this.$message.error('文件大小不能超过10MB');
        return false;
      }

      if (!isTypeValid) {
        this.$message.error('只允许上传PPTX和DOCX文件');
        return false;
      }

      const pptCount = this.fileUpload.fileList.filter(file => file.type === 'application/vnd.openxmlformats-officedocument.presentationml.presentation').length;
      const docCount = this.fileUpload.fileList.filter(file => file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document').length;

      if (file.type === 'application/vnd.openxmlformats-officedocument.presentationml.presentation' && pptCount >= 1) {
        this.$message.error('只允许上传一个PPT文件');
        return false;
      }

      if (file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' && docCount >= 1) {
        this.$message.error('只允许上传一个Word文件');
        return false;
      }

      return true;
    },

    // 文件上传
    handleFileChange({ fileList }) {
      const MAX_FILE_SIZE_MB = 10;
      const VALID_TYPES = [
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ];

      // 过滤掉不符合大小和类型的文件
      this.fileUpload.fileList = fileList.filter(file => {
        const isSizeValid = file.size / 1024 / 1024 <= MAX_FILE_SIZE_MB;
        const isTypeValid = VALID_TYPES.includes(file.type);
        return isSizeValid && isTypeValid;
      }).map(file => {
        // Ensure the URL is correctly set
        let url = file.url;
        if (!url && file.response && file.response.success) {
          url = file.response.result;
        }
        return {
          name: file.name,
          url,
          type: file.type,
          size: file.size,
          status: file.status || 'done',
          uid: file.uid
        };
      });

      // 确保只保留一个PPT文件和一个Word文件
      const pptFiles = this.fileUpload.fileList.filter(file => file.type === 'application/vnd.openxmlformats-officedocument.presentationml.presentation');
      const docFiles = this.fileUpload.fileList.filter(file => file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');

      this.fileUpload.fileList = [...pptFiles.slice(0, 1), ...docFiles.slice(0, 1)];

      // 更新 model.attach
      this.model.attach = JSON.stringify(this.fileUpload.fileList);
    },

    fetchPageData() {
      // 加载标签数据
      getAction(`/sys/dictItem/all`, { dictCode: "biz_scenic_tag" })
        .then((res) => {
          if (res.success) {
            this.scenicTagList = res.result;
          } else {
            this.$message.warning(res.message || '请求错误');
          }
        })
        .catch((res) => {
          this.$message.warning(res.message || '网络错误');
        });

      // 加载用户标签数据
      getAction(`/sys/dictItem/all`, { dictCode: "biz_scenic_user_tag" })
        .then((res) => {
          if (res.success) {
            this.scenicUserTagList = res.result;
          } else {
            this.$message.warning(res.message || '请求错误');
          }
        })
        .catch((res) => {
          this.$message.warning(res.message || '网络错误');
        });
    },
    save() {
      // 数据校验
      if (!this.model.scenicName) {
        this.$message.error('景区名称必须填写');
        return false;
      }
      // 处理图片链接
      if (this.upload.fileList != null && this.upload.fileList.length > 0) {
        this.model.imgUrl = this.upload.fileList.join(",");
      } else {
        this.model.imgUrl = "";
      }
      // 处理封面图片链接
      if (this.upload.coverFileList != null && this.upload.coverFileList.length > 0) {
        this.model.coverImgUrl = this.upload.coverFileList.join(",");
      } else {
        this.model.coverImgUrl = "";
      }
      // 处理附件
      if (this.fileUpload.fileList && this.fileUpload.fileList.length > 0) {
        this.model.attach = JSON.stringify(this.fileUpload.fileList);
      }
      if (this.$route.params.id > 0) {
        this.edit();
      } else {
        this.add();
      }
    },
    add() {
      this.loading = true;
      let params = Object.assign({}, this.model);
      params.scenicTag = JSON.stringify(this.model.scenicTag || []);
      params.scenicUserTag = JSON.stringify(this.model.scenicUserTag || []);
      postAction(this.url.add, params)
        .then((res) => {
          if (res.success) {
            this.$message.success(res.message || '操作成功');
            this.closeCurrent();
          } else {
            this.$message.error(res.message || '请求错误');
          }
        })
        .catch((res) => {
          this.$message.error(res.message || '网络错误');
        })
        .finally(() => {
          this.loading = false;
        });
    },
    edit() {
      this.loading = true;
      this.model.id = this.$route.params.id;
      let params = Object.assign({}, this.model);

      // 首先，提取 scenicTagList 中所有 itemText 的值
      const tagList = this.scenicTagList.map((item) => item.itemText);

      // 确保 this.model.scenicTag 是一个数组，如果不是则设为空数组
      this.model.scenicTag = Array.isArray(this.model.scenicTag) ? this.model.scenicTag : [];
      // 然后，使用 filter 和 includes 方法过滤掉不存在于提取的 tagList 中的标签
      this.model.scenicTag = this.model.scenicTag.filter((tag) => tagList.includes(tag));

      const userTagList = this.scenicUserTagList.map((item) => item.itemText);

      // 确保 this.model.scenicUserTag 是一个数组，如果不是则设为空数组
      this.model.scenicUserTag = Array.isArray(this.model.scenicUserTag) ? this.model.scenicUserTag : [];
      this.model.scenicUserTag = this.model.scenicUserTag.filter((tag) => userTagList.includes(tag));

      params.scenicTag = JSON.stringify(this.model.scenicTag);
      params.scenicUserTag = JSON.stringify(this.model.scenicUserTag);

      putAction(this.url.edit, params)
        .then((res) => {
          if (res.success) {
            this.$message.success(res.message || '操作成功');
            this.closeCurrent();
          } else {
            this.$message.warning(res.message || '请求错误');
          }
          this.loading = false;
        })
        .catch((res) => {
          this.$message.warning(res.message || '网络错误');
        });
    },
    reset() {
      this.model = Object.assign({}, this.resetModel);
      this.area = [];
      this.upload.fileList = [];
      this.upload.coverFileList = [];
      this.fileUpload.fileList = [];
      this.$destroy('UploadImage');
    },
    openUrlModal() {
      this.urlModalVisible = true;
      this.inputUrl = '';
      this.urlError = '';
    },
    closeUrlModal() {
      this.urlModalVisible = false;
    },
    startProgress() {
      this.collectProgress = 0;
      this.collectInterval = setInterval(() => {
        if (this.collectProgress < 90) {
          this.collectProgress += Math.floor(Math.random() * 10) + 1;
        } else if (this.collectProgress < 95) {
          this.collectProgress += Math.floor(Math.random() * 3) + 1;
        } else {
          clearInterval(this.collectInterval);
        }
      }, 300);
    },
    stopProgress() {
      clearInterval(this.collectInterval);
      this.collectProgress = 100;
      setTimeout(() => {
        this.collectLoading = false;
      }, 500);
    },
    validateAndFetchUrl() {
      const scenicUrlRegex = /^https:\/\/you\.ctrip\.com\/sight\/.*?\/\d+\.html($|\?|#)/i;
      if (!scenicUrlRegex.test(this.inputUrl)) {
        this.urlError = '请输入有效的景区链接';
        return;
      }

      this.urlError = '';
      this.loading = true;
      this.collectLoading = true;
      this.startProgress();

      // 使用 getAction
      getAction('/biz/bizScenic/collect', { url: this.inputUrl })
        .then(res => {
          this.stopProgress();
          if (res.success) {
            res.result.scenicTag = JSON.parse(res.result.scenicTag);
            res.result.scenicUserTag = JSON.parse(res.result.scenicUserTag);
            this.model = res.result;
            this.area = [this.model.regionProvince, this.model.regionCity, this.model.regionArea];
            if (this.model.imgUrl != null && this.model.imgUrl.length > 0) {
              this.upload.fileList = this.model.imgUrl.split(",");
            }
            if (this.model.coverImgUrl != null && this.model.coverImgUrl.length > 0) {
              this.upload.coverFileList = this.model.coverImgUrl.split(",");
            }
            this.loadFinished = true;
            this.closeUrlModal();
            this.$message.success('采集成功');
          } else {
            console.error(res.message || '链接无效');
            this.$message.error(res.message || '链接无效');
          }
        })
        .catch(error => {
          this.stopProgress();
          console.error(error);
          this.$message.error('网络错误');
        })
        .finally(() => {
          this.loading = false;
          this.collectLoading = false;
        });
    },
    // 打开地图选点器
    openMapPicker() {
      this.$refs.mapPicker.show();
    },
    
    // 地图选点回调
    onMapSelect(coordinates) {
      this.model.longitude = coordinates.longitude;
      this.model.latitude = coordinates.latitude;
      this.$message.success('坐标设置成功');
    },
    
    // 地址信息回调
    onAddressInfo(addressInfo) {
      if (addressInfo.address && !this.model.regionAddress) {
        this.model.regionAddress = addressInfo.address;
      }
      
      if (!this.model.scenicName && addressInfo.title && addressInfo.category && 
          (addressInfo.category.includes('景') || addressInfo.category.includes('博物馆') || 
           addressInfo.category.includes('公园') || addressInfo.category.includes('文物'))) {
        this.$confirm({
          title: '发现景点信息',
          content: `是否使用搜索到的景点名称："${addressInfo.title}"？`,
          onOk: () => {
            this.model.scenicName = addressInfo.title;
          }
        });
      }
    },
  }
};
</script>

<style lang="scss" scoped>
::v-deep .ant-card-body {
  padding-left: 15px;
}

.form {
  padding: 0 100px;
}

.my-form-item {
  display: flex;
  display: -webkit-flex;

  /* Safari */
  textarea {
    width: 500px;
  }

  input {
    width: 500px;
  }

  .btn-left {
    margin-left: 20px;
  }

  a-cascader {
    width: 200px;
  }
}

.my-form-item-upload {
  width: 600px;
}

.checkbox-area {
  width: 600px;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.my-center {
  text-align: center;

  .btn-left {
    margin-left: 20px;
  }
}

.title {
  display: flex;
  flex-direction: row;
  font-size: 16px;
  margin: 20px 0 10px;
  align-items: center;

  .spot {
    width: 6px;
    height: 16px;
    margin: 0 8px 0 0;
    background: #5ac78a;
    border-radius: 6px;
  }
}

.ImgModal {
  ::v-deep .ant-modal-body {
    padding: 24px !important;
  }

  ::v-deep .ant-modal-close {
    .ant-modal-close-x {
      display: block;
      width: 36px;
      height: 36px;
      font-size: 16px;
      font-style: normal;
      line-height: 36px;
      text-align: center;
      text-transform: none;
      text-rendering: auto;
    }
  }
}

.grey-background {
  background-color: #f0f0f0;
  /* 浅灰色 */
  padding: 10px;
  border-radius: 4px;
}

.form-group {
  background-color: #f0f0f0;
  /* 浅灰色 */
  padding: 0px;
  border-radius: 0px;
  margin-bottom: 0px;
  /*调整为你想要的间距 */
}
</style>
