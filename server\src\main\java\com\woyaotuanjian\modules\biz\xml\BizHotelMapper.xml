<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woyaotuanjian.modules.biz.mapper.BizHotelMapper">

    <select id="getBizHotelExtList" resultType="com.woyaotuanjian.modules.biz.entity.pojo.BizHotelExt">
        select sql_calc_found_rows
        s.*,u.realname sysUserName
        from biz_hotel s
        left join sys_user u on s.sys_user_id=u.id
        <where>
            <if test="hotelName!=null and hotelName!=''"> and s.hotel_name like concat('%',#{hotelName},'%')</if>
            <if test="hotelStar!=null and hotelStar!=''"> and s.hotel_star=#{hotelStar}</if>
            <!-- 地理位置筛选 -->
            <if test="regionProvince!=null and regionProvince!=''"> and s.region_province=#{regionProvince}</if>
            <if test="regionCity!=null and regionCity!=''"> and s.region_city=#{regionCity}</if>
            <if test="regionArea!=null and regionArea!=''"> and s.region_area=#{regionArea}</if>
            <if test="comGroup!=null and comGroup!=''">
                and ( s.com_id in (${comGroup})
                        <if test="currentUserId!=null">or s.sys_user_id=#{currentUserId}</if>
                    )
            </if>
        </where>
        group by s.id
        order by s.status asc
        limit ${start},${num}
    </select>
    <select id="getHotelPanelList" resultType="com.woyaotuanjian.modules.biz.entity.pojo.BizHotelExt">
        select s.*,u.realname sysUserName
        from biz_hotel s
        left join sys_user u on s.sys_user_id=u.id
        <where>
            and s.status=1
            <if test="regionCity!=null and regionCity!=''">and s.region_city like concat('%',#{regionCity},'%')</if>
            <if test="hotelName!=null and hotelName!=''">and s.hotel_name like concat('%',#{hotelName},'%')</if>
            <if test="search!=null and search!=''">and (s.hotel_name like concat('%',#{search},'%') or s.region like concat('%',#{search},'%'))</if>
            
            <!-- 地区筛选条件 -->
            <if test="regionFilters != null and regionFilters.size() > 0">
                and (
                <foreach collection="regionFilters" item="filter" separator=" or ">
                    (
                    <if test="filter.province != null and filter.province != ''">
                        s.region_province = #{filter.province}
                    </if>
                    <if test="filter.city != null and filter.city != '' and filter.province != null and filter.province != ''">
                        and s.region_city = #{filter.city}
                    </if>
                    <if test="filter.city != null and filter.city != '' and (filter.province == null or filter.province == '')">
                        s.region_city = #{filter.city}
                    </if>
                    <if test="filter.area != null and filter.area != '' and filter.city != null and filter.city != ''">
                        and s.region_area = #{filter.area}
                    </if>
                    <if test="filter.area != null and filter.area != '' and (filter.city == null or filter.city == '')">
                        s.region_area = #{filter.area}
                    </if>
                    )
                </foreach>
                )
            </if>
            
            <if test="comGroup!=null and comGroup!=''">
                and ( s.com_id in (${comGroup})
                    <if test="currentUserId!=null"> or s.sys_user_id=#{currentUserId}</if>
                )
            </if>
        </where>
        group by s.id
        order by
        <if test="currentUserId!=null and currentUserId>0">s.sys_user_id=#{currentUserId} desc,</if>
        s.search_index desc,s.id asc
        limit 200
    </select>
</mapper>