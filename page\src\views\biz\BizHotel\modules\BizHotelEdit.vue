<template>
  <a-spin :spinning="loading">
    <a-card :bordered="false">
      <a-form layout="horizontal" class="form">
        <a-form-item label="酒店名称" class="my-form-item">
          <a-input v-model="model.hotelName" placeholder="酒店名称" style='width: 350px;' />
          <a-input v-model="model.hotelSimpleName" placeholder="简称" style='width: 110px;margin-left: 10px;' />
          <a-tooltip title="输入酒店链接，可自动填空">
            <a-icon type="robot" style="margin-left: 10px; color: aqua;" @click="openUrlModal" />
          </a-tooltip>
        </a-form-item>
        <a-form-item label="特色简述" class="my-form-item">
          <a-input v-model="model.advantageDesc" placeholder="一句话描述酒店特色" />
        </a-form-item>
        <a-form-item label="酒店介绍" class="my-form-item">
          <a-textarea placeholder="用于酒店图文详细介绍素材，支持 # 插入图片" class="textarea" :autoSize="{ minRows: 6, maxRows: 30 }"
            v-model="model.hotelDesc"></a-textarea>
        </a-form-item>
        <!--自动生成图文介绍和导出word时是否自动填写酒店介绍素材-->
        <a-form-item label="自动填充" class="my-form-item">
          <a-switch default-checked v-model="model.autoFillingDesc" /><span style="color:#C0C0C0">
            行程图文介绍是否自动填充该酒店</span>
        </a-form-item>
        <a-form-item label="行程介绍" class="my-form-item">
          <a-textarea placeholder="用于行程时间表展现，支持 * 分割多个子行程" class="textarea" :autoSize="{ minRows: 3, maxRows: 30 }"
            v-model="model.tripDesc"></a-textarea>
        </a-form-item>
        <a-form-item label="搜索指数" class="my-form-item">
          <a-input-number style="width:100px;" placeholder="搜索指数" v-model="model.searchIndex" />
        </a-form-item>
        <a-form-item class="my-form-item" label="地理位置">
          <a-cascader popupClassName="media-edit-cascader" @change="onAreaChange" change-on-select :options="areaData"
            v-model="area" :showSearch="true" placeholder="请选择" />
        </a-form-item>
        <!-- <a-form-item label="详细地址" class="my-form-item">
          <a-input v-model="model.regionAddress" placeholder="请输入详细地址" />
        </a-form-item> -->
        <a-form-item label="地理坐标" class="my-form-item">
          <a-input-number placeholder="经度" v-model="model.longitude" style="width: 120px;" :precision="6" />
          <span style="margin: 0 10px;">，</span>
          <a-input-number placeholder="纬度" v-model="model.latitude" style="width: 120px;" :precision="6" />
          <a-button type="primary" style="margin-left: 10px;" @click="openMapPicker">
            <a-icon type="environment" />地图选点
          </a-button>
        </a-form-item>
        <a-form-item label="特色标签" class="my-form-item checkbox-area">
          <a-checkbox-group v-model="model.hotelTag">
            <div style="display: inline-block;margin-right:8px;" v-for="(item,index) in this.hotelTagList" :key="index">
              <a-checkbox :name="item.itemText" :value="item.itemText">{{ item.itemText }}</a-checkbox>
            </div>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item label="酒店星级" class="my-form-item">
          <j-dict-select-tag style="width:100px;" v-model="model.hotelStar" placeholder="请选择星级"
            dictCode="biz_hotel_star" />
        </a-form-item>

        <!-- 成本和成本备注区域 -->
        <div class="form-group" v-if="this.isInputCost">
          <a-form-item label="成本" class="my-form-item">
            <a-input-number placeholder="成本" v-model="model.cost" style="width: 100px;" />
            <button type="button" class="ant-btn dark-grey-btn" style="margin-left: 10px;" @click="openCostModal">设置成本</button>
          </a-form-item>
          <a-form-item label="成本备注" class="my-form-item">
            <a-textarea
              placeholder="成本备注，供定制师参考"
              class="textarea"
              :auto-size="{ minRows: 3, maxRows: 30 }"
              v-model="model.costRemark"
            ></a-textarea>
          </a-form-item>
        </div>

        <a-form-item label="房价" class="my-form-item">
          <span>默认 </span>
          <a-input-number :default-value="0" v-model="model.price" placeholder="0" />
          <span>元 </span>
          <button type="button" class="ant-btn ant-btn-primary" style="margin-right:10px;" @click="openPriceModal">设置价格</button>
        </a-form-item>
        <a-form-item label="费用备注" class="my-form-item">
          <a-textarea placeholder="酒店报价说明, 儿童早餐等. 用于word导出报价部分" class="textarea"
            :auto-size="{ minRows: 3, maxRows: 30 }" v-model="model.priceRemark"></a-textarea>
        </a-form-item>
        <a-form-item label="司陪房价" class="my-form-item">
          <a-input-number v-model="model.driverPrice" placeholder="0" />
        </a-form-item>
        <a-form-item label="介绍图" class="my-form-item-upload">
          <div class="clearfix">
            <upload-image :key="'hotel_' + model.id" @getImageId="getImageId" @delImageId="delImageId" :multiple="30"
              :img="upload.fileList" @draggable="handleDraggable" v-if="loadFinished"></upload-image>
          </div>
        </a-form-item>
        <a-form-item label="封面图" class="my-form-item-upload">
          <div class="clearfix">
            <upload-image :key="'hotel_cover_' + model.id" @getImageId="getImageIdCover" @delImageId="delImageIdCover"
              :multiple="2" :max-file-size="5" :img="upload.coverFileList" @draggable="handleDraggableCover"
              v-if="loadFinished"></upload-image>
          </div>
        </a-form-item>
        <a-form-item label="附件" class="my-form-item-upload">
          <div class="attach-box">
            <a-upload name="file" :action="uploadUrl" :fileList="fileUpload.fileList" :beforeUpload="beforeFileUpload"
              @change="handleFileChange" :showUploadList="{ showRemoveIcon: true, showPreviewIcon: true }">
              <a-button>
                <a-icon type="upload" />
                上传附件
              </a-button>
            </a-upload>
          </div>
        </a-form-item>
        <!--暂时屏蔽网络链接
        <a-form-item label="网络链接" class="my-form-item" >
          <a-input v-model="model.thirdUrl" placeholder="网络链接"/>
        </a-form-item>
        -->
        <a-form-item label="状态" class="my-form-item">
          <j-dict-select-tag style="width:200px;" v-model="model.status" placeholder="状态" dictCode="biz_hotel_status" />
        </a-form-item>
        <a-form-item class="my-center">
          <button type="button" class="ant-btn ant-btn-primary" @click="save"><span>确 定</span></button>
          <button type="button" class="ant-btn" style="margin-left:10px;" @click="this.closeCurrent"><span>关 闭</span></button>
        </a-form-item>
      </a-form>
      <set-price-modal ref="priceModal" @afterSetPrice="afterSetPrice"></set-price-modal>
      <set-price-modal ref="costModal" @afterSetPrice="afterSetCost"></set-price-modal>
    </a-card>

    <!-- 采集网络链接弹窗 -->
    <a-modal :visible="urlModalVisible" title="输入酒店链接" @ok="validateAndFetchUrl" @cancel="closeUrlModal">
      <a-form-item label="网址">
        <a-input v-model="inputUrl" placeholder="请输入酒店链接" />
      </a-form-item>
      <span v-if="urlError" style="color:red">{{ urlError }}</span>
      <div class="hint">
        <a-icon type="info-circle" style="color:orange;" />
        <span
          style="margin-left: 8px;">温馨提示：目前仅支持携程酒店链接<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;例如：https://hotels.ctrip.com/hotels/95749195.html</span>
      </div>
      <a-progress :percent="collectProgress" v-if="collectLoading" />

      <template slot="footer">
        <a-button key="back" @click="closeUrlModal">取消</a-button>
        <a-button key="submit" type="primary" @click="validateAndFetchUrl" :loading="collectLoading">
          确定
        </a-button>
      </template>
    </a-modal>
    
    <!-- 地图选点组件 -->
    <map-picker 
      ref="mapPicker"
      :initial-longitude="model.longitude"
      :initial-latitude="model.latitude"
      :initial-keyword="model.hotelName"
      search-region="全国"
      search-category="酒店,宾馆,旅馆,住宿"
      @select="onMapSelect"
      @address-info="onAddressInfo"
    />
  </a-spin>
</template>

<script>
import { getAction, postAction, putAction } from '@/api/manage';
import { RouterLinkMixinConfig } from '@/mixins/RouterLinkMixinConfig';
import SetPriceModal from '@comp/priceModal/SetPriceModal.vue';
import ChineseDistricts from '@/assets/distpicker.data.min.js';
import { axios } from '@/utils/request';
import UploadImage from '@comp/UploadImage/UploadImage';
import MapPicker from '@/components/MapPicker/MapPicker.vue';

// 处理地区数组
function getCityArr(cityJson) {
  let Pobj = cityJson[1];
  let Pvalue = Object.keys(Pobj);

  let option = Pvalue.map(item => {
    let arr = { value: Pobj[item], label: Pobj[item] };
    if (Object.keys(cityJson).includes(item)) {
      arr.children = getCityArrChild(cityJson, item);
    }
    return arr;
  });
  return option;
}

function getCityArrChild(cityJson, key) {
  let Cobj = cityJson[key];
  let Cvalue = Object.keys(Cobj);

  let Coption = Cvalue.map(item => {
    let Carr = { value: Cobj[item], label: Cobj[item] };
    if (Object.keys(cityJson).includes(item)) {
      Carr.children = getCityArrChild(cityJson, item);
    }
    return Carr;
  });
  return Coption;
}

export default {
  name: 'BizHotelEdit',
  mixins: [RouterLinkMixinConfig],
  inject: ['closeCurrent', 'onCloseCurrent'],
  components: {
    ChineseDistricts, SetPriceModal, UploadImage, MapPicker
  },
  activated() {
    this.loading = false;
    this.fetchPageData();
    if (this.$route.params.id) {
      this.model.id = this.$route.params.id;
      this.getEditData();
    } else {
      this.loadFinished = true;
    }
  },
  data() {
    return {
      model: {
        id: null,
        advantageDesc: null,
        hotelDesc: null,
        autoFillingDesc: false,
        tripDesc: null,
        searchIndex: 0,
        price: null,
        priceRule: null,
        cost: null,
        costRule: null,
        costRemark: null,
        driverPrice: null,
        region: null,
        regionAddress: null,
        regionArea: null,
        regionCity: null,
        regionCountry: null,
        regionProvince: null,
        longitude: null,
        latitude: null,
        hotelName: null,
        hotelSimpleName: null,
        status: "1",
        priceRemark: "",
        hotelTag: [],
        imgUrl: null,
        coverImgUrl: null,
        attach: null,
        thirdUrl: null,
      },
      resetModel: {
        id: null,
        advantageDesc: null,
        hotelDesc: null,
        autoFillingDesc: false,
        tripDesc: null,
        searchIndex: 0,
        price: null,
        priceRule: null,
        cost: null,
        costRule: null,
        costRemark: null,
        driverPrice: null,
        region: null,
        regionAddress: null,
        regionArea: null,
        regionCity: null,
        regionCountry: null,
        regionProvince: null,
        longitude: null,
        latitude: null,
        hotelName: null,
        hotelSimpleName: null,
        status: "1",
        priceRemark: "",
        hotelTag: [],
        imgUrl: null,
        coverImgUrl: null,
        attach: null,
        thirdUrl: null,
      },
      upload: {
        fileList: [],
        coverFileList: [],
      },
      //附件
      fileUpload: {
        fileList: [],
      },
      //景区标签
      hotelTagList: [],
      //全部区域数据
      areaData: {},
      //选择出来的区域数组，省-市-县
      area: [],
      loading: true,
      collectLoading: false,
      loadFinished: false,
      urlModalVisible: false,
      inputUrl: '',
      urlError: '',
      isInputCost: false,
      url: {
        add: "/biz/bizHotel/add",
        get: "/biz/bizHotel/queryById",
        edit: "/biz/bizHotel/edit",
        collect: "/biz/bizHotel/collect",
        getCom: "/biz/bizCompany/queryById"
      },
      collectProgress: 0,
      collectInterval: null,
    };
  },
  computed: {
    uploadUrl() {
      let url = (axios.defaults.baseURL || "") + '/file/upload';
      return url;
    }
  },
  mounted() {
    this.sysUser = this.$store.getters.userInfo;
    // 如果comID为0或者null，则不获取com信息
    if (this.sysUser.comId == 0 || this.sysUser.comId == null) {
      return;
    }
    // 根据this.sysUser.comID获取com信息
    getAction(this.url.getCom, { id: this.sysUser.comId })
      .then((res) => {
        if (res.success) {
          this.model.comName = res.result.comName;
          // 获取config中的enableInputCost
          if (res.result.config != null) {
            let config = JSON.parse(res.result.config);
            this.isInputCost = config.enableCostInput;
            // 如果sysUser.roleCode.startsWith('biz')不成立，则不显示成本
            if (!this.sysUser.roleCode.startsWith('biz')) {
              this.isInputCost = false;
            }
          }
        } else {
          this.$message.warning(res.message || '请求错误');
        }
      })
      .catch((res) => {
        this.$message.warning(res.message || '网络错误');
      });
  },
  created() {
    // this.onCloseCurrent(this.$route.path,this.reset)
    this.onCloseCurrent(this.$route.path, () => {
      this.reset();
      this.$router.push({ path: "/biz/bizHotel/list" });
    });
    this.areaData = getCityArr(ChineseDistricts);
  },
  methods: {
    afterSetPrice(priceRule) {
      this.model.priceRule = JSON.stringify(priceRule);
    },
    afterSetCost(costRule) {
      this.model.costRule = JSON.stringify(costRule);
    },
    openPriceModal() {
      let priceRule = JSON.parse(this.model.priceRule || "{}");
      this.$refs.priceModal.initModal('hotel', null, priceRule);
      this.$refs.priceModal.title = '设置价格';
    },
    openCostModal() {
      let costRule = JSON.parse(this.model.costRule || "{}");
      this.$refs.costModal.initModal('hotel', null, costRule);
      this.$refs.costModal.title = '设置成本价格';
    },
    onAreaChange(value) {
      this.model.regionProvince = value[0];
      this.model.regionCity = value[1];
      this.model.regionArea = value[2];
      this.model.regionCountry = '中国';
      this.model.region = value.join("-");
    },
    getEditData() {
      //加载编辑数据
      getAction(this.url.get, { id: this.model.id })
        .then(res => {
          if (res.success) {
            this.model = res.result;
            this.model.createTime = null;
            this.model.updateTime = null;
            //设置区域
            this.area = [this.model.regionProvince, this.model.regionCity, this.model.regionArea];
            //设置图片
            if (this.model.imgUrl != null && this.model.imgUrl.length > 0) {
              this.upload.fileList = this.model.imgUrl.split(",");
            }
            //设置封面图片
            if (this.model.coverImgUrl != null && this.model.coverImgUrl.length > 0) {
              this.upload.coverFileList = this.model.coverImgUrl.split(",");
            }
            // 文件回显
            if (res.result.attach != null && res.result.attach.length > 0) {
              this.fileUpload.fileList = JSON.parse(res.result.attach);
            }
            this.loadFinished = true;
          } else {
            this.$message.warning(res.message || '请求错误');
          }
        })
        .catch(res => {
          this.$message.warning(res.message || '网络错误');
        });
    },
    //获取上传的图片
    getImageId: function (val) {
      this.upload.fileList = this.upload.fileList.concat(val);
    },
    //删除图片
    delImageId: function (val) {
      this.upload.fileList = this.upload.fileList.filter((x) => x !== val);
    },
    //获取到重新排序后的图片
    handleDraggable(e) {
      this.upload.fileList = e;
    },

    //获取上传的封面图片
    getImageIdCover: function (val) {
      this.upload.coverFileList = this.upload.coverFileList.concat(val);
    },
    //删除封面图片
    delImageIdCover: function (val) {
      this.upload.coverFileList = this.upload.coverFileList.filter((x) => x !== val);
    },
    //获取到重新排序后的封面图片
    handleDraggableCover(e) {
      this.upload.coverFileList = e;
    },
    // 上传文件前的校验
    beforeFileUpload(file) {
      const MAX_FILE_SIZE_MB = 10; // 10MB 大小限制
      const VALID_TYPES = [
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ];

      const isSizeValid = file.size / 1024 / 1024 <= MAX_FILE_SIZE_MB;
      const isTypeValid = VALID_TYPES.includes(file.type);

      if (!isSizeValid) {
        this.$message.error('文件大小不能超过10MB');
        return false;
      }

      if (!isTypeValid) {
        this.$message.error('只允许上传PPTX和DOCX文件');
        return false;
      }

      const pptCount = this.fileUpload.fileList.filter(file => file.type === 'application/vnd.openxmlformats-officedocument.presentationml.presentation').length;
      const docCount = this.fileUpload.fileList.filter(file => file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document').length;

      if (file.type === 'application/vnd.openxmlformats-officedocument.presentationml.presentation' && pptCount >= 1) {
        this.$message.error('只允许上传一个PPT文件');
        return false;
      }

      if (file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' && docCount >= 1) {
        this.$message.error('只允许上传一个Word文件');
        return false;
      }

      return true;
    },
    // 文件上传
    handleFileChange({ fileList }) {
      const MAX_FILE_SIZE_MB = 10;
      const VALID_TYPES = [
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ];

      // 过滤掉不符合大小和类型的文件
      this.fileUpload.fileList = fileList.filter(file => {
        const isSizeValid = file.size / 1024 / 1024 <= MAX_FILE_SIZE_MB;
        const isTypeValid = VALID_TYPES.includes(file.type);
        return isSizeValid && isTypeValid;
      }).map(file => {
        // Ensure the URL is correctly set
        let url = file.url;
        if (!url && file.response && file.response.success) {
          url = file.response.result;
        }
        return {
          name: file.name,
          url,
          type: file.type,
          size: file.size,
          status: file.status || 'done',
          uid: file.uid
        };
      });

      // 确保只保留一个PPT文件和一个Word文件
      const pptFiles = this.fileUpload.fileList.filter(file => file.type === 'application/vnd.openxmlformats-officedocument.presentationml.presentation');
      const docFiles = this.fileUpload.fileList.filter(file => file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');

      this.fileUpload.fileList = [...pptFiles.slice(0, 1), ...docFiles.slice(0, 1)];

      // 更新 model.attach
      this.model.attach = JSON.stringify(this.fileUpload.fileList);
    },
    fetchPageData() {
      //加载标签数据
      getAction(`/sys/dictItem/all`, { dictCode: "biz_hotel_tag" })
        .then(res => {
          if (res.success) {
            this.hotelTagList = res.result;

          } else {
            this.$message.warning(res.message || '请求错误');
          }
        })
        .catch(res => {
          this.$message.warning(res.message || '网络错误');
        });
    },
    save() {
      if (!this.model.hotelName) {
        this.$message.error('酒店名称必须填写');
        return false;
      }
      //处理图片链接
      if (this.upload.fileList != null && this.upload.fileList.length > 0) {
        this.model.imgUrl = this.upload.fileList.join(",");
      } else {
        this.model.imgUrl = "";
      }
      //处理封面图片链接
      if (this.upload.coverFileList != null && this.upload.coverFileList.length > 0) {
        this.model.coverImgUrl = this.upload.coverFileList.join(",");
      } else {
        this.model.coverImgUrl = "";
      }
      // 处理附件
      if (this.fileUpload.fileList && this.fileUpload.fileList.length > 0) {
        this.model.attach = JSON.stringify(this.fileUpload.fileList);
      }
      if (this.model.id && this.model.id > 0) {
        this.edit();
      } else {
        this.add();
      }
    },
    add() {
      this.loading = true;
      let params = Object.assign({}, this.model);
      params.hotelTag = JSON.stringify(this.model.hotelTag || []);
      postAction(this.url.add, params)
        .then(res => {
          if (res.success) {
            this.$message.success(res.message || '操作成功');
            this.closeCurrent();
            //操作
          } else {
            this.$message.error(res.message || '请求错误');
          }
          this.loading = false;
        })
        .catch(res => {
          this.$message.error(res.message || '网络错误');
        })
        .finally(() => {
          this.loading = false;
        });
    },
    edit() {
      this.loading = true;
      let params = Object.assign({}, this.model);
      params.hotelTag = JSON.stringify(this.model.hotelTag || []);
      putAction(this.url.edit, params)
        .then(res => {
          if (res.success) {
            this.$message.success(res.message || '操作成功');
            this.closeCurrent();
            //操作
          } else {
            this.$message.warning(res.message || '请求错误');
          }
          this.loading = false;
        }).catch(res => {
          this.$message.warning(res.message || '网络错误');
        });
    },
    reset() {
      this.model = Object.assign({}, this.resetModel);
      this.area = [];
      this.upload.fileList = [];
      this.upload.coverFileList = [];
      this.fileUpload.fileList = [];
      this.$destroy('UploadImage');
    },
    openUrlModal() {
      this.urlModalVisible = true;
      this.inputUrl = '';
      this.urlError = '';
    },
    closeUrlModal() {
      this.urlModalVisible = false;
    },
    startProgress() {
      this.collectProgress = 0;
      this.collectInterval = setInterval(() => {
        if (this.collectProgress < 90) {
          this.collectProgress += Math.floor(Math.random() * 10) + 1;
        } else if (this.collectProgress < 95) {
          this.collectProgress += Math.floor(Math.random() * 3) + 1;
        } else {
          clearInterval(this.collectInterval);
        }
      }, 300);
    },
    stopProgress() {
      clearInterval(this.collectInterval);
      this.collectProgress = 100;
      setTimeout(() => {
        this.collectLoading = false;
      }, 500);
    },
    validateAndFetchUrl() {
      // 支持携程酒店链接格式: hotels.ctrip.com/hotels/123456.html 或 hotels.ctrip.com/hotels/detail/?hotelId=123456
      const ctripUrlRegex = /^https:\/\/hotels\.ctrip\.com\/hotels\/(detail\/\?.*hotelId=\d+.*|\d+\.html.*$)/i;
      if (!ctripUrlRegex.test(this.inputUrl)) {
        this.urlError = '请输入有效的酒店链接';
        return;
      }

      this.urlError = '';
      this.loading = true;
      this.collectLoading = true;
      this.startProgress();

      // 使用 getAction
      getAction(this.url.collect, { url: this.inputUrl })
        .then(res => {
          this.stopProgress();
          if (res.success) {
            //将res.result中的数据赋值给model
            res.result.hotelTag = JSON.parse(res.result.hotelTag);
            this.model = res.result;
            this.area = [this.model.regionProvince, this.model.regionCity, this.model.regionArea];
            if (this.model.imgUrl != null && this.model.imgUrl.length > 0) {
              this.upload.fileList = this.model.imgUrl.split(",");
            }
            if (this.model.coverImgUrl != null && this.model.coverImgUrl.length > 0) {
              this.upload.coverFileList = this.model.coverImgUrl.split(",");
            }
            this.loadFinished = true;
            this.closeUrlModal();
            this.$message.success('采集成功');
          } else {
            console.error(res.message || '链接无效');
            this.$message.error(res.message || '链接无效');
          }
        })
        .catch(error => {
          this.stopProgress();
          console.error(error);
          this.$message.error('网络错误');
        })
        .finally(() => {
          this.loading = false;
          this.collectLoading = false;
        });
    },
    
    // 打开地图选点器
    openMapPicker() {
      this.$refs.mapPicker.show();
    },
    
    // 地图选点回调
    onMapSelect(coordinates) {
      this.model.longitude = coordinates.longitude;
      this.model.latitude = coordinates.latitude;
      this.$message.success('坐标设置成功');
    },
    
    // 地址信息回调
    onAddressInfo(addressInfo) {
      if (addressInfo.address && !this.model.regionAddress) {
        this.model.regionAddress = addressInfo.address;
      }
      
      if (!this.model.hotelName && addressInfo.title) {
        this.$confirm({
          title: '发现酒店信息',
          content: `是否使用搜索到的酒店名称："${addressInfo.title}"？`,
          onOk: () => {
            this.model.hotelName = addressInfo.title;
          }
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .ant-card-body {
  padding-left: 15px;
}

.form {
  padding: 0 100px;
}
.my-form-item-upload{
  width: 600px;
}
.my-form-item{
  display: flex;
  .textarea{
    width: 500px;
  }
  input{
    width: 500px;
  }
  .btn-left{
    margin-left: 20px;
  }
  a-cascader{
    width:200px;
  }
}
.media-edit-cascader{
  width: 500px;
}
.checkbox-area{
  width: 600px;
  flex-wrap: wrap ;
  justify-content:flex-start;
}
 .my-center{
   text-align: center;
  .btn-left{
    margin-left: 20px;
  }
 }
.title {
  display: flex;
  flex-direction: row;
  font-size: 16px;
  margin: 20px 0 10px;
  align-items: center;
  .spot {
    width: 6px;
    height: 16px;
    margin: 0 8px 0 0;
    background: #5ac78a;
    border-radius: 6px;
  }
}
.ant-input-number{
  width: 200px;
}
.ImgModal{
   ::v-deep .ant-modal-body{
     padding: 24px !important;
   }
   ::v-deep .ant-modal-close{
     .ant-modal-close-x {
      display: block;
      width: 36px;
      height: 36px;
      font-size: 16px;
      font-style: normal;
      line-height: 36px;
      text-align: center;
      text-transform: none;
      text-rendering: auto;
    }
   }
  }

.form-group {
  background-color: #f0f0f0; /* 浅灰色 */
  padding: 0px;
  border-radius: 0px;
  margin-bottom: 0px; /* 调整为你想要的间距 */
}

.dark-grey-btn {
  background-color: #606060; /* 深灰色 */
  color: white;
  border: none;
}
</style>
