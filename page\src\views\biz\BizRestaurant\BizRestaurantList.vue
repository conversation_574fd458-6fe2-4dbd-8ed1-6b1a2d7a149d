<template>
  <a-card :bordered="false">

    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">

          <a-col :md="6" :sm="8">
            <a-form-item label="餐厅名称">
              <a-input placeholder="请输入餐厅名称" @keyup.enter="searchQuery" v-model="queryParam.restName"></a-input>
            </a-form-item>
          </a-col>

          <a-col :md="6" :sm="8">
            <a-form-item label="地理位置">
              <a-cascader 
                v-model="queryParam.regionCascader"
                :options="areaData"
                change-on-select
                :showSearch="true"
                placeholder="请选择地理位置"
                @change="onRegionChange"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>

          <a-col :md="6" :sm="8" >
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">刷新</a-button>
              <!-- <a-button type="primary" @click="handleAdd" icon="plus" style="margin-left: 8px">新增</a-button> -->
              <a-button type="primary" @click="openTab('biz-bizRestaurant-edit')" icon="plus" style="margin-left: 8px">新增</a-button>
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">

    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowClassName="setRowClassName"
        :customRow="setColor"
        @change="handleTableChange"
        >

        <span slot="action" slot-scope="text, record">
         <!-- 如果是多com共享基础数据,则com_id为0,不允许biz操作 -->
          <a-dropdown v-if="sysUser.roleCode=='admin'||((sysUser.roleCode=='biz' || sysUser.roleCode=='bizSuper')&&(record.comId != 0))||sysUser.id==record.sysUserId">
            <a class="ant-dropdown-link">操作 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="openTab('biz-bizRestaurant-edit',record)">编辑</a>
              </a-menu-item>                  
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <bizRestaurant-modal ref="modalForm" @ok="modalFormOk"></bizRestaurant-modal>
  </a-card>
</template>

<script>
  import BizRestaurantModal from './modules/BizRestaurantModal'
  import { JeecgListMixin } from '@/mixins/JeecgListMixinCopy'
  import { RouterLinkMixinConfig } from '@/mixins/RouterLinkMixinConfig';
  import ChineseDistricts from '@/assets/distpicker.data.min.js'

  // 处理地区数组
  function getCityArr(cityJson) {
    let Pobj = cityJson[1];
    let Pvalue = Object.keys(Pobj);

    let option = Pvalue.map(item => {
      let arr = { value: Pobj[item], label: Pobj[item] };
      if (Object.keys(cityJson).includes(item)) {
        arr.children = getCityArrChild(cityJson, item);
      }
      return arr;
    });
    return option;
  }

  function getCityArrChild(cityJson, key) {
    let Cobj = cityJson[key];
    let Cvalue = Object.keys(Cobj);

    let Coption = Cvalue.map(item => {
      let Carr = { value: Cobj[item], label: Cobj[item] };
      if (Object.keys(cityJson).includes(item)) {
        Carr.children = getCityArrChild(cityJson, item);
      }
      return Carr;
    });
    return Coption;
  }

  export default {
    name: "BizRestaurantList",
    mixins:[JeecgListMixin,RouterLinkMixinConfig],
    components: {
      BizRestaurantModal
    },
    data () {
      return {
        description: '餐厅管理页面',
        sysUser:{},
        // 全部区域数据
        areaData: [],
        // 表头
        columns: [
           {
            title: '#',
            align:"center",
            dataIndex: 'id'
           },          
           {
            title: '名称',
            align:"center",
            dataIndex: 'restName'
           },
          {
            title: '搜索指数',
            align:"center",
            sorter:  (a, b) => a.searchIndex - b.searchIndex,
            dataIndex: 'searchIndex'
           },
           {
            title: '菜系',
            align:"center",
            dataIndex: 'restFoodType'
           },    
           {
            title: '最低餐标',
            align:"center",
            dataIndex: 'price'
           }, 
          {
            title: '特色标签',
            align:"center",
            dataIndex: 'restTag'
           },                   
           {
            title: '简介',
            align:"center",
            dataIndex: 'advantageDesc'
           },
           {
            title: '位置',
            align:"center",
            dataIndex: 'region'
           },
           {
            title: '更新时间',
            align:"center",
            dataIndex: 'updateTime'
           },
           {
            title: '状态',
            align:"center",
            dataIndex: 'status_dictText'
           },
          {
            title: '作者',
            align:"center",
            dataIndex: 'sysUserName'
           },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            scopedSlots: { customRender: 'action' },
          }
        ],
		url: {
          list: "/biz/bizRestaurant/list",
          delete: "/biz/bizRestaurant/delete",
          deleteBatch: "/biz/bizRestaurant/deleteBatch",
          exportXlsUrl: "biz/bizRestaurant/exportXls",
          importExcelUrl: "biz/bizRestaurant/importExcel",
       },
    }
  },
  computed: {
    importExcelUrl: function(){
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
    }
  },
  created() {
    // 初始化地区数据
    this.areaData = getCityArr(ChineseDistricts);
  },
  mounted() {
    this.sysUser=this.$store.getters.userInfo
  },  
  methods: {
    onRegionChange(value) {
      // 地理位置选择变化时的处理
      if (value && value.length > 0) {
        this.queryParam.regionProvince = value[0];
        this.queryParam.regionCity = value.length > 1 ? value[1] : null;
        this.queryParam.regionArea = value.length > 2 ? value[2] : null;
      } else {
        this.queryParam.regionProvince = null;
        this.queryParam.regionCity = null;
        this.queryParam.regionArea = null;
      }
    },
  }
}
</script>
<style scoped>
  @import '~@assets/less/common.less'
</style>