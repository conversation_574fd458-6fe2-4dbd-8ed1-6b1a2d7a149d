// src/main/java/com/woyaotuanjian/modules/biz/entity/vo/TripDetailVO.java

package com.woyaotuanjian.modules.biz.entity.vo;

import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

@Data
public class TripDetailVO {
    // 基础信息
    private Long tripId;
    private String tripName;
    private String tripFullName;
    private Integer dayNum;
    private Integer status;
    private String headerImageUrl;
    private List<String> tagList;
    private BigDecimal tripPrice;
    private String companyCode;
    private String scrollTip;      // 滚动提示内容

    // AI增强内容
    private String designerRecommend;
    private String tripFeatures;
    private List<DaySchedule> tripDayList;
    private String contactInfo;    // 联系方式
    private Integer showContact;   // 是否显示联系我们 0-不显示 1-显示
    private String warmTips;      // 温馨提示
    private Integer showTime;     // 是否显示时间点 0-不显示 1-显示
    private Integer showDesigner; // 是否显示定制师卡片 0-不显示 1-显示

    // 价格相关字段
    private String priceMode;            // 报价模式: none/detailed/simple，不存在时表示未开启报价
    private BigDecimal totalPrice;       // 总价
    private PriceInfo priceInfo;         // 价格详情

    private Integer createBy;  // 创建人id
    private DesignerInfo designerInfo; // 定制师信息

    /**
     * 是否显示行程安排 0-不显示 1-显示
     */
    private Integer showSchedule;
    private Integer showMap;
    
    /**
     * 附件信息，多个附件用逗号分隔，每个附件格式为：文件路径|原始文件名
     */
    private String attach;

    private Integer fullScreen;     // 是否全屏显示 0-不全屏 1-全屏

    @Data
    public static class PriceInfo {
        private BasicInfo basicInfo;           // 基本信息
        private List<PriceCategory> categories;// 价格分类(分项报价模式)
        private List<SimplePrice> simplePrices;// 简单价格列表(简易报价模式)
        private List<IncludedFee> includedFees;// 费用包括项
        private List<ExcludedFee> excludedFees;// 费用不含项
        private String custInputContent; // 填空报价内容
    }

    @Data
    public static class BasicInfo {
        private String departureDate;   // 出发日期,格式:2024-11-10T13:12:58.211Z
        private Integer adultCount;      // 成人数
        private Integer childCount;      // 儿童数
        private Integer roomCount;       // 房间数
    }

    @Data
    public static class PriceCategory {
        private String type;            // 分类类型
        private String name;            // 分类名称
        private List<PriceItem> items;  // 分类下的价格项
    }

    @Data
    public static class PriceItem {
        private String name;            // 费用名称
        private BigDecimal unitPrice;   // 单价
        private String unit;            // 单位
        private String customUnit;      // 自定义单位
        private Integer quantity;       // 数量
        private BigDecimal total;       // 小计
        private String remark;          // 备注
    }

    @Data
    public static class SimplePrice {
        private String category;        // 类别
        private BigDecimal unitPrice;   // 单价
        private Integer quantity;       // 数量
        private BigDecimal total;       // 小计
    }

    @Data
    public static class IncludedFee {
        private String name;            // 费用名称
        private String remark;          // 备注说明
    }

    @Data
    public static class ExcludedFee {
        private String name;            // 费用名称
        private String remark;          // 备注说明
    }

    @Data
    public static class DaySchedule {
        private Integer day;
        private String tabName;
        private String dayTheme;
        private Integer showTimeline;
        private List<TimelinePoint> tripPointList;
        private List<ScenicInfo> scenicList;
        private List<HotelInfo> hotelList;
        private List<RestaurantInfo> restList;
        private List<WordInfo> wordList;
        
    }

    @Data
    public static class TimelinePoint {
        private String timeStr;
        private String title;
        private String content;
    }

    @Data
    public static class ScenicInfo {
        private Long id;
        private String name;
        private String imageUrl;
        private String startTime;
    }

    @Data
    public static class HotelInfo {
        private Long id;
        private String name;
        private String imageUrl;
        private String star;
        private String startTime;
    }

    @Data
    public static class RestaurantInfo {
        private Long id;
        private String name;
        private String imageUrl;
        private String startTime;
    }

    @Data
    public static class WordInfo {
        private Long id;
        private String name;
        private String wordScence;
        private String startTime;
    }

    @Data
    public static class DesignerInfo {
        private String realname;     // 真实姓名
        private String avatar;       // 头像
        private String position;     // 职位
        private String motto;        // 个性签名
        private Integer workYears;   // 工作年限
        private Integer serviceCount;// 服务人数
        private String phone;//电话号码
    }
}