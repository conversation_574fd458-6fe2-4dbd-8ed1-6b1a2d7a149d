<template>
  <a-card :bordered="false">

    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">

          <a-col :md="6" :sm="8">
            <a-form-item label="酒店名称">
              <a-input placeholder="请输入酒店名称" @keyup.enter="searchQuery" v-model="queryParam.hotelName"></a-input>
            </a-form-item>
          </a-col>

          <a-col :md="6" :sm="8">
            <a-form-item label="地理位置">
              <a-cascader 
                v-model="queryParam.regionCascader"
                :options="areaData"
                change-on-select
                :showSearch="true"
                placeholder="请选择地理位置"
                @change="onRegionChange"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>

          <!-- 暂时注释掉星级搜索
          <a-col :md="4" :sm="8">
            <a-form-item label="酒店星级">
              <j-dict-select-tag
                v-model="queryParam.hotelStar"
                placeholder="酒店星级"
                dictCode="biz_hotel_star"
              />
            </a-form-item>
          </a-col>-->

          <a-col :md="6" :sm="8" >
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">刷新</a-button>
              <!-- <a-button type="primary" @click="handleAdd" icon="plus" style="margin-left: 8px">新增</a-button> -->
              <a-button type="primary" @click="openTab('biz-bizHotel-edit')" icon="plus" style="margin-left: 8px">新增</a-button>
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">

    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowClassName="setRowClassName"
        :customRow="setColor"
        @change="handleTableChange"
        >

        <span slot="action" slot-scope="text, record">
          <!-- 如果是多com共享基础数据,则com_id为0,不允许biz操作 -->
          <a-dropdown v-if="sysUser.roleCode=='admin'||((sysUser.roleCode=='biz' || sysUser.roleCode=='bizSuper')&&(record.comId != 0))||sysUser.id==record.sysUserId">
            <a class="ant-dropdown-link">操作 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item> 
                <a @click="openTab('biz-bizHotel-edit',record)">编辑</a>
              </a-menu-item> 
              <a-menu-item>
                <a-popconfirm title="确定复制吗?" @confirm="() => copy(record)">
                  <a>复制</a>
                </a-popconfirm>
              </a-menu-item>             
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <bizHotel-modal ref="modalForm" @ok="modalFormOk"></bizHotel-modal>
    <set-price-modal ref="priceModal" @afterSetPrice="afterSetPrice"></set-price-modal>
  </a-card>
</template>

<script>
  import BizHotelModal from './modules/BizHotelModal'
  import { JeecgListMixin } from '@/mixins/JeecgListMixinCopy'
  import { RouterLinkMixinConfig } from '@/mixins/RouterLinkMixinConfig';
  import SetPriceModal from '@comp/priceModal/SetPriceModal.vue'
  import { httpAction,getAction } from '@/api/manage'
  import ChineseDistricts from '@/assets/distpicker.data.min.js'

  // 处理地区数组
  function getCityArr(cityJson) {
    let Pobj = cityJson[1];
    let Pvalue = Object.keys(Pobj);

    let option = Pvalue.map(item => {
      let arr = { value: Pobj[item], label: Pobj[item] };
      if (Object.keys(cityJson).includes(item)) {
        arr.children = getCityArrChild(cityJson, item);
      }
      return arr;
    });
    return option;
  }

  function getCityArrChild(cityJson, key) {
    let Cobj = cityJson[key];
    let Cvalue = Object.keys(Cobj);

    let Coption = Cvalue.map(item => {
      let Carr = { value: Cobj[item], label: Cobj[item] };
      if (Object.keys(cityJson).includes(item)) {
        Carr.children = getCityArrChild(cityJson, item);
      }
      return Carr;
    });
    return Coption;
  }

  export default {
    name: "BizHotelList",
    mixins:[JeecgListMixin,RouterLinkMixinConfig],
    components: {
      BizHotelModal,SetPriceModal
    },
    data () {
      return {
        description: '酒店管理管理页面',
        sysUser:{},
        // 全部区域数据
        areaData: [],
        // 表头
        columns: [
           {
            title: '#',
            align:"center",
            dataIndex: 'id'
           },          
           {
            title: '酒店名称',
            align:"center",
            dataIndex: 'hotelName'
           },
             {
            title: '搜索指数',
            align:"center",
            sorter:  (a, b) => a.searchIndex - b.searchIndex,
            dataIndex: 'searchIndex'
           },
           {
            title: '酒店星级',
            align:"center",
            dataIndex: 'hotelStar'
           },
           {
            title: '特色标签',
            align:"center",
            dataIndex: 'hotelTag'
           },

           {
            title: '特色',
            align:"center",
            dataIndex: 'advantageDesc',
            ellipsis:true,
            width:200
           },           
           {
             title: '位置',
            align:"center",
            dataIndex: 'region'
           },
           {
            title: '更新时间',
            align:"center",
            dataIndex: 'updateTime'
           },
           {
            title: '状态',
            align:"center",
            dataIndex: 'status_dictText'
           },
          {
            title: '作者',
            align:"center",
            dataIndex: 'sysUserName'
           },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            scopedSlots: { customRender: 'action' },
          }
        ],
		url: {
          list: "/biz/bizHotel/list",
          copy: "/biz/bizHotel/copy",
          delete: "/biz/bizHotel/delete",
          deleteBatch: "/biz/bizHotel/deleteBatch",
          exportXlsUrl: "biz/bizHotel/exportXls",
          importExcelUrl: "biz/bizHotel/importExcel",
       },
    }
  },
  computed: {
    importExcelUrl: function(){
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
    }
  },
  created() {
    // 初始化地区数据
    this.areaData = getCityArr(ChineseDistricts);
  },
  mounted() {
    this.sysUser=this.$store.getters.userInfo
  },  
  methods: {
    onRegionChange(value) {
      // 地理位置选择变化时的处理
      if (value && value.length > 0) {
        this.queryParam.regionProvince = value[0];
        this.queryParam.regionCity = value.length > 1 ? value[1] : null;
        this.queryParam.regionArea = value.length > 2 ? value[2] : null;
      } else {
        this.queryParam.regionProvince = null;
        this.queryParam.regionCity = null;
        this.queryParam.regionArea = null;
      }
    },
    afterSetPrice(priceRule){
      console.log("子组件设置priceRule成功",priceRule)
      this.model.priceRule=JSON.stringify(priceRule)
    },
    openPriceModal(record){
      this.$refs.priceModal.initModal('hotel',record.id);
      this.$refs.priceModal.title = '设置价格';
    },
    copy(record){
      httpAction(this.url.copy,record,"post").then((res)=>{
        if(res.success){
          this.$message.success(res.message);
          this.openTab('biz-bizHotel-edit',res.result)
        }else{
          this.$message.warning(res.message);
        }
      }).finally(() => {

      })
    },
  }
}
</script>
<style scoped>
  @import '~@assets/less/common.less'
</style>