package com.woyaotuanjian.modules.biz.controller;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.woyaotuanjian.common.api.vo.Result;
import com.woyaotuanjian.common.aspect.annotation.AutoLog;
import com.woyaotuanjian.common.constant.RoleConstant;
import com.woyaotuanjian.common.system.vo.LoginUser;
import com.woyaotuanjian.modules.biz.entity.BizHotel;
import com.woyaotuanjian.modules.biz.entity.pojo.BizHotelExt;
import com.woyaotuanjian.modules.biz.entity.pojo.RefreshCmd;
import com.woyaotuanjian.modules.biz.mapper.BizHotelMapper;
import com.woyaotuanjian.modules.biz.service.*;
import com.woyaotuanjian.modules.biz.util.ExpressionParserUtil;
import com.woyaotuanjian.modules.biz.util.SysUserUtil;
import com.woyaotuanjian.modules.biz.util.YdUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @Description: 酒店管理
 * @Author: jeecg-boot
 * @Date:   2021-06-21
 * @Version: V1.0
 */
@Slf4j
@Api(tags="酒店管理")
@RestController
@RequestMapping("/biz/bizHotel")
public class BizHotelController {
	@Autowired
	private IBizHotelService bizHotelService;
	@Resource
	private BizHotelMapper bizHotelMapper;
	@Autowired
	private IBizTripService bizTripService;
	@Autowired
	private IBizCompanyService companyService;
	@Autowired
	private AiService aiService;
	@Autowired
	private FileService fileService;
	/**
	  * 分页列表查询
	 * @param bizHotelExt
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "酒店管理-分页列表查询")
	@ApiOperation(value="酒店管理-分页列表查询", notes="酒店管理-分页列表查询")
	@GetMapping(value = "/list")
	public Result queryPageList(BizHotelExt bizHotelExt,
									  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
									  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
									  @RequestParam(name="onlyCheckedCom", defaultValue="1") Integer onlyCheckedCom,
									  HttpServletRequest req) {
		Result result = new Result();

		Map<String,Object> param=YdUtil.sqlMap(req.getParameterMap());
		LoginUser sysUser=SysUserUtil.getCurrentUser();
		//根据登陆人过滤数据
		companyService.dataListFilter(param,sysUser,onlyCheckedCom);
		IPage<BizHotelExt> pageList = bizHotelService.getBizHotelExtList(new Page(pageNo, pageSize), param);
		result.setSuccess(true);
		result.setResult(pageList);
		return result;
	}
	
	/**
	  *   添加
	 * @param bizHotel
	 * @return
	 */
	@AutoLog(value = "酒店管理-添加")
	@ApiOperation(value="酒店管理-添加", notes="酒店管理-添加")
	@PostMapping(value = "/add")
	@RequiresRoles(value = {RoleConstant.ADMIN,RoleConstant.B1,RoleConstant.B2SUPER},logical = Logical.OR)
	public Result<BizHotel> add(@RequestBody BizHotelExt bizHotel) {
		Result result = new Result<>();
		try {
			// 检查酒店名称是否重复
			QueryWrapper<BizHotel> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("hotel_name", bizHotel.getHotelName());
			// 如果是B2用户，只检查自己创建的数据
			LoginUser loginUser = SysUserUtil.getCurrentUser();
			if(loginUser.getRoleCode().startsWith(RoleConstant.B2)) {
				queryWrapper.eq("sys_user_id", loginUser.getId());
			} else {
				// 如果是其他用户，检查同公司下的数据
				queryWrapper.eq("com_id", loginUser.getComId());
			}
			int count = bizHotelService.count(queryWrapper);
			if(count > 0) {
				result.error500("酒店名称已存在！");
				return result;
			}

			// 原有的添加逻辑
			Date now = new Date();
			bizHotel.setVersion(0);
			bizHotel.setCreateTime(now);
			bizHotel.setUpdateTime(now);
			bizHotel.setSysUserId(loginUser.getId());
			if(loginUser.getRoleCode().equals(RoleConstant.ADMIN)){//管理员
				bizHotel.setComId(loginUser.getComId());
			}else if(loginUser.getRoleCode().startsWith(RoleConstant.B1)){//b1或者b1super
				bizHotel.setComId(loginUser.getComId());
			}else if(loginUser.getRoleCode().startsWith(RoleConstant.B2)) {//b2或者b2super
				//私有数据，不添加com
			}
			bizHotelService.save(bizHotel);
			result.success("添加成功！");
			result.setResult(bizHotel);
		} catch (Exception e) {
			log.error(e.getMessage(),e);
			result.error500("操作失败");
		}
		return result;
	}

	/**
	 *   复制
	 * @param bizHotel
	 * @return
	 */
	@AutoLog(value = "酒店-复制")
	@ApiOperation(value="酒店-复制", notes="酒店-复制")
	@PostMapping(value = "/copy")
	@RequiresRoles(value = {RoleConstant.ADMIN,RoleConstant.B1,RoleConstant.B2SUPER},logical = Logical.OR)
	public Result copy(@RequestBody BizHotel bizHotel) {
		try {
			BizHotel dbHotel = bizHotelService.getById(bizHotel.getId());
			if (dbHotel == null) {
				return Result.error("未找到对应实体");
			}
			dbHotel.setId(null);
			dbHotel.setVersion(0);
			dbHotel.setCreateTime(new Date());
			dbHotel.setUpdateTime(new Date());
			dbHotel.setHotelName("复制-" + dbHotel.getHotelName());
			dbHotel.setSysUserId(SysUserUtil.getCurrentUser().getId());
			dbHotel.setComId(SysUserUtil.getCurrentUser().getComId());
			dbHotel.setSearchIndex(0);//不复制搜索指数
			bizHotelService.save(dbHotel);
			return Result.ok(dbHotel);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Result.error("操作失败！");
		}
	}

	/**
	  *  编辑
	 * @param bizHotel
	 * @return
	 */
	@AutoLog(value = "酒店管理-编辑")
	@ApiOperation(value="酒店管理-编辑", notes="酒店管理-编辑")
	@PutMapping(value = "/edit")
	public Result edit(@RequestBody BizHotelExt bizHotel) {
		Result result = new Result<>();
		BizHotel bizHotelEntity = bizHotelService.getById(bizHotel.getId());
		if(bizHotelEntity==null) {
			result.error500("未找到对应实体");
		}else {

			LoginUser loginUser=SysUserUtil.getCurrentUser();
			//检查编辑和删除权限
			Result check=companyService.checkBasicDataPermission(loginUser,bizHotelEntity.getComId(),bizHotelEntity.getSysUserId());
			if(!check.isSuccess()){
				return check;
			}

			bizHotel.setUpdateTime(new Date());
			boolean ok = bizHotelService.updateById(bizHotel);
			Set<String> cmd=new HashSet<>();
			cmd.add(RefreshCmd.DEFAULT);
			if(bizHotel.getAutoFillingDesc()){
				cmd.add(RefreshCmd.REFRESH_RICHTEXT);
			}
			bizTripService.refreshTrip("hotel",bizHotel.getId(), cmd);
			result.success("修改成功!");
		}
		
		return result;
	}
	
	/**
	  *   通过id删除
	 * @param id
	 * @return
	 */
	@AutoLog(value = "酒店管理-通过id删除")
	@ApiOperation(value="酒店管理-通过id删除", notes="酒店管理-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result delete(@RequestParam(name="id",required=true) String id) {
		Result<BizHotel> result = new Result<BizHotel>();
		BizHotel bizHotel = bizHotelService.getById(id);
		if(bizHotel==null) {
			result.error500("未找到对应实体");
		}else {

			LoginUser loginUser=SysUserUtil.getCurrentUser();
			//检查编辑和删除权限
			Result check=companyService.checkBasicDataPermission(loginUser,bizHotel.getComId(),bizHotel.getSysUserId());
			if(!check.isSuccess()){
				return check;
			}

			List<Long> associatedTripIDList = bizTripService.getAssociatedTripIdList("hotel",bizHotel.getId());
			if (associatedTripIDList.size()>0){
				String retStr = "行程已使用,不允许删除. 数量：" + associatedTripIDList.size()+",详细："
						+ associatedTripIDList.stream().limit(10).collect(Collectors.toList()).toString();
				if (associatedTripIDList.size()>10){
					retStr = retStr + "……";
				}
				return Result.error(retStr);
			}

			boolean ok = bizHotelService.removeById(id);
			if(ok) {
				result.success("删除成功!");
			}
		}
		
		return result;
	}

	
	/**
	  * 通过id查询
	 * @param id
	 * @return
	 */
	@AutoLog(value = "酒店管理-通过id查询")
	@ApiOperation(value="酒店管理-通过id查询", notes="酒店管理-通过id查询")
	@GetMapping(value = "/queryById")
	public Result queryById(@RequestParam(name="id",required=true) String id) {
		Result result = new Result<>();
		BizHotel bizHotel = bizHotelService.getById(id);
		JSONObject resJson = (JSONObject) JSONObject.toJSON(bizHotel);

		resJson.put("hotelTag", JSONArray.parse(bizHotel.getHotelTag()));
		if(bizHotel==null) {
			result.error500("未找到对应实体");
		}else {
			result.setResult(resJson);
			result.setSuccess(true);
		}
		return result;
	}

	/**
	 * 面板查询
	 * @param req
	 * @return
	 */
	@ApiOperation(value="酒店-面板查询", notes="酒店-面板查询")
	@GetMapping(value = "/hotelDataList")
	public Result hotelDataList(HttpServletRequest req,@RequestParam(value = "search",required = false)String search) {
		HashMap<String,Object> param=YdUtil.sqlMap(req.getParameterMap());
		if(search!=null&&search.equals("null")){
			param.remove("search");
		}
		
		// 处理地区筛选参数
		List<Map<String, String>> regionFilters = new ArrayList<>();
		Map<String, String[]> parameterMap = req.getParameterMap();
		
		// 提取地区筛选参数
		for (String key : parameterMap.keySet()) {
			if (key.startsWith("regionProvince") || key.startsWith("regionCity") || key.startsWith("regionArea")) {
				String indexStr = key.replaceAll("[^0-9]", ""); // 提取数字索引
				if (!indexStr.isEmpty()) {
					int index = Integer.parseInt(indexStr);
					
					// 确保regionFilters有足够的元素
					while (regionFilters.size() <= index) {
						regionFilters.add(new HashMap<>());
					}
					
					String value = parameterMap.get(key)[0];
					if (key.startsWith("regionProvince")) {
						regionFilters.get(index).put("province", value);
					} else if (key.startsWith("regionCity")) {
						regionFilters.get(index).put("city", value);
					} else if (key.startsWith("regionArea")) {
						regionFilters.get(index).put("area", value);
					}
				}
			}
		}
		
		// 将地区筛选参数添加到param中
		if (!regionFilters.isEmpty()) {
			param.put("regionFilters", regionFilters);
		}
		
		LoginUser sysUser=SysUserUtil.getCurrentUser();
		//根据登陆人过滤数据
		companyService.panelDataListFilter(param,sysUser);

		//我的数据优先
		param.put("currentUserId",sysUser.getId());

		List<BizHotelExt> list = bizHotelMapper.getHotelPanelList(param);
		list.stream().forEach(item->{
			if(YdUtil.empty(item.getRegionCity())){
				item.setRegionCity("全国");
			}
			item.setHotelDesc(ExpressionParserUtil.filterExpression(item.getHotelDesc()));
		});
		//全部城市列表
		List<String> allCityList=list.stream().map(BizHotel::getRegionCity).distinct().collect(Collectors.toList());
		allCityList.add(0,"全部");

		//所有景区名称按城市分组
		Map<String,List<String>> cityMap=new ConcurrentHashMap<>();
		//cityMap.put("全部",list.stream().map(BizHotel::getHotelName).collect(Collectors.toList()));
		List<String> allHotelNameList = new ArrayList<>(100);

		//所有数据改为Map<name,data>，方便查找
		Map<String, BizHotelExt> nameMap=new ConcurrentHashMap<>();
		list.stream().forEach(item->{
			String name=item.getHotelName();
			//不同用户可能创建相同的名称，如有相同，则加 @用户名 的后缀，方便识别
			if (allHotelNameList.contains(name)){
				name = name +"@" +item.getSysUserName();
			}
			allHotelNameList.add(name);
			nameMap.put(name,item);

			List<String> nameList=cityMap.get(item.getRegionCity());
			if(nameList==null){
				nameList=new ArrayList<>(10);
			}
			nameList.add(name);
			cityMap.put(item.getRegionCity(),nameList);
		});
		cityMap.put("全部",allHotelNameList);

		JSONObject resp=new JSONObject();
		resp.put("cityList",allCityList);
		resp.put("cityMap",cityMap);
		resp.put("nameMap",nameMap);
		return Result.ok(resp);
	}

	//按照上面的格式，增加一个controller方法，接受的输入参数是一个url字符串，如果正确，返回的是bizHotel对象，否则返回错误信息
	//url格式：/biz/bizHotel/collect?url=xxx
	@ApiOperation(value="酒店-通过url采集酒店数据", notes="酒店-通过url采集酒店数据")
	@GetMapping(value = "/collect")
	public Result collect(@RequestParam(name="url",required=true) String url) {
		Result result = new Result<>();
		try {
			BizHotel bizHotel = bizHotelService.collect(url);
			result.success("采集成功！");
			result.setResult(bizHotel);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			result.error500("操作失败:"+e.getMessage());
		}
		return result;
	}

	/**
	 * 批量获取酒店坐标
	 * @param ids 酒店ID列表，逗号分隔
	 * @return 酒店坐标列表
	 */
	@AutoLog(value = "酒店-批量获取坐标")
	@ApiOperation(value="酒店-批量获取坐标", notes="酒店-批量获取坐标")
	@GetMapping(value = "/coordinates")
	public Result getCoordinates(@RequestParam(name="ids",required=true) String ids) {
		Result result = new Result<>();
		try {
			String[] idArray = ids.split(",");
			List<Map<String, Object>> coordinates = new ArrayList<>();
			
			for (String id : idArray) {
				if (id != null && !id.trim().isEmpty()) {
					BizHotel hotel = bizHotelService.getById(id.trim());
					if (hotel != null && hotel.getLongitude() != null && hotel.getLatitude() != null) {
						Map<String, Object> coord = new HashMap<>();
						coord.put("id", hotel.getId());
						coord.put("name", hotel.getHotelName());
						coord.put("longitude", hotel.getLongitude());
						coord.put("latitude", hotel.getLatitude());
						coord.put("type", "hotel");
						coordinates.add(coord);
					}
				}
			}
			
			result.setSuccess(true);
			result.setResult(coordinates);
		} catch (Exception e) {
			log.error("获取酒店坐标失败:", e);
			result.error500("获取坐标失败:" + e.getMessage());
		}
		return result;
	}

}
