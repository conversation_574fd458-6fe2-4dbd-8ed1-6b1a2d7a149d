<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woyaotuanjian.modules.biz.mapper.BizScenicMapper">

    <select id="getBizScenicExtList" resultType="com.woyaotuanjian.modules.biz.entity.pojo.BizScenicExt">
        select sql_calc_found_rows
           s.*,
           u.realname sysUserName
        from biz_scenic s
        left join sys_user u on s.sys_user_id=u.id
        <where>
            <if test="scenicName!=null and scenicName!=''">
                and s.scenic_name like concat('%',#{scenicName},'%')
            </if>
            <!-- 地理位置筛选 -->
            <if test="regionProvince!=null and regionProvince!=''"> and s.region_province=#{regionProvince}</if>
            <if test="regionCity!=null and regionCity!=''"> and s.region_city=#{regionCity}</if>
            <if test="regionArea!=null and regionArea!=''"> and s.region_area=#{regionArea}</if>
            <if test="comGroup!=null and comGroup!=''">
                and ( s.com_id in (${comGroup})
                <if test="currentUserId!=null">or s.sys_user_id=#{currentUserId}</if>
                )
            </if>
        </where>
        group by s.id
        order by
        s.search_index desc,s.status asc,s.id asc
        limit ${start},${num}
    </select>
    <select id="getScenicPanelList" resultType="com.woyaotuanjian.modules.biz.entity.pojo.BizScenicExt">
        select
           s.*,
           u.realname sysUserName
        from biz_scenic s
        left join sys_user u on s.sys_user_id=u.id
        <where>
            and s.status=1
            <if test="regionCity!=null and regionCity!=''">and s.region_city like concat('%',#{regionCity},'%')</if>
            <if test="scenicName!=null and scenicName!=''">and s.scenic_name like concat('%',#{scenicName},'%')</if>
            <if test="search!=null and search!=''">and (s.scenic_name like concat('%',#{search},'%') or s.region like concat('%',#{search},'%'))</if>
            
            <!-- 地区筛选条件 -->
            <if test="regionFilters != null and regionFilters.size() > 0">
                and (
                <foreach collection="regionFilters" item="filter" separator=" or ">
                    (
                    <if test="filter.province != null and filter.province != ''">
                        s.region_province = #{filter.province}
                    </if>
                    <if test="filter.city != null and filter.city != '' and filter.province != null and filter.province != ''">
                        and s.region_city = #{filter.city}
                    </if>
                    <if test="filter.city != null and filter.city != '' and (filter.province == null or filter.province == '')">
                        s.region_city = #{filter.city}
                    </if>
                    <if test="filter.area != null and filter.area != '' and filter.city != null and filter.city != ''">
                        and s.region_area = #{filter.area}
                    </if>
                    <if test="filter.area != null and filter.area != '' and (filter.city == null or filter.city == '')">
                        s.region_area = #{filter.area}
                    </if>
                    )
                </foreach>
                )
            </if>
            
            <if test="comGroup!=null and comGroup!=''">
                and ( s.com_id in (${comGroup})
                <if test="currentUserId!=null"> or s.sys_user_id=#{currentUserId}</if>
                )
            </if>
        </where>
        group by s.id
        order by
        <if test="currentUserId!=null and currentUserId>0">s.sys_user_id=#{currentUserId} desc,</if>
        s.search_index desc,s.id asc
        limit 200
    </select>
</mapper>