package com.woyaotuanjian.modules.biz.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.woyaotuanjian.modules.biz.entity.*;
import com.woyaotuanjian.modules.biz.entity.vo.TripDetailVO;
import com.woyaotuanjian.modules.biz.entity.vo.TripMapDataVO;
import com.woyaotuanjian.modules.biz.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AppTripServiceImpl implements IAppTripService {

    @Autowired
    private IBizTripService bizTripService;
    @Autowired
    private IBizTripAiContentService aiContentService;
    @Autowired
    private IBizScenicService bizScenicService;
    @Autowired
    private IBizHotelService bizHotelService;
    @Autowired
    private IBizRestaurantService bizRestaurantService;
    @Autowired
    private IBizWordService bizWordService;

    @Override
    public TripDetailVO getTripDetail(String shopId, String tripId) {
        // 1. 获取基础行程信息
        BizTrip trip = bizTripService.getTripByCode(tripId);
        if (trip == null) {
            log.warn("行程不存在,tripId: {}", tripId);
            return null;
        }

        // 2. 构建返回对象
        TripDetailVO detailVO = new TripDetailVO();

        // 3. 设置基础信息
        detailVO.setTripId(trip.getId());
        detailVO.setTripName(trip.getTripName());
        detailVO.setTripFullName(trip.getTripFullName());
        detailVO.setStatus(Integer.parseInt(trip.getStatus()));
        detailVO.setDayNum(trip.getDayNum());
        detailVO.setTripPrice(trip.getPrice());
        detailVO.setCompanyCode(trip.getCode());
        detailVO.setCreateBy(trip.getSysUserId());

        if (StringUtils.isNotEmpty(trip.getTripTag())) {
            detailVO.setTagList(JSON.parseArray(trip.getTripTag(), String.class));
        }

        // 4. 获取AI内容
        BizTripAiContent aiContent = aiContentService.getByTripId(trip.getId());
        
        // 获取滚动提示内容
        if (aiContent != null && StringUtils.isNotEmpty(aiContent.getScrollTip())) {
            detailVO.setScrollTip(aiContent.getScrollTip());
        } 
        
        // 设置AI内容
        if (aiContent != null && aiContent.getStatus() == 1) {
            detailVO.setDesignerRecommend(aiContent.getDesignerRecommend());
            detailVO.setTripFeatures(aiContent.getAiFeatures());
            // 设置轮播图
            detailVO.setHeaderImageUrl(aiContent.getCarouselImages());
            
            // 设置联系方式相关信息
            detailVO.setContactInfo(aiContent.getContactInfo());
            detailVO.setShowContact(aiContent.getShowContact());
            detailVO.setWarmTips(aiContent.getWarmTips());
            
            // 添加是否显示行程安排字段
            detailVO.setShowSchedule(aiContent.getShowSchedule());
            detailVO.setShowMap(aiContent.getShowMap());
            detailVO.setFullScreen(aiContent.getFullScreen());
            detailVO.setShowTime(aiContent.getShowTime());
            detailVO.setShowDesigner(aiContent.getShowDesigner());
              
            // 添加附件字段
            detailVO.setAttach(aiContent.getAttach());

            // 处理行程安排
            if (aiContent.getAiSchedule() != null) {
                try {
                    detailVO.setTripDayList(JSON.parseObject(aiContent.getAiSchedule(),
                            new TypeReference<List<TripDetailVO.DaySchedule>>(){}));
                } catch (Exception e) {
                    log.error("Parse trip schedule failed", e);
                    detailVO.setTripDayList(Collections.emptyList());
                }
            }

            // 处理价格信息
            processPriceInfo(aiContent, detailVO);
        }

        return detailVO;
    }

    @Override
    public TripMapDataVO getTripMapData(String shopId, String tripId) {
        try {
            // 1. 获取基础行程信息
            BizTrip trip = bizTripService.getTripByCode(tripId);
            if (trip == null) {
                log.warn("行程不存在,tripId: {}", tripId);
                return null;
            }

            // 2. 获取行程详情
            TripDetailVO tripDetail = getTripDetail(shopId, tripId);
            if (tripDetail == null) {
                log.warn("行程详情不存在,tripId: {}", tripId);
                return null;
            }

            // 3. 从行程基础信息中提取各类ID
            List<String> scenicIds = new ArrayList<>();
            List<String> hotelIds = new ArrayList<>();
            List<String> restaurantIds = new ArrayList<>();
            List<String> wordIds = new ArrayList<>();

            // 从 trip 实体中提取 ID 列表
            if (StringUtils.isNotEmpty(trip.getScenicIds())) {
                try {
                    List<String> ids = JSON.parseArray(trip.getScenicIds(), String.class);
                    scenicIds.addAll(ids);
                } catch (Exception e) {
                    log.warn("解析景点ID失败: {}", trip.getScenicIds());
                }
            }

            if (StringUtils.isNotEmpty(trip.getHotelIds())) {
                try {
                    List<String> ids = JSON.parseArray(trip.getHotelIds(), String.class);
                    hotelIds.addAll(ids);
                } catch (Exception e) {
                    log.warn("解析酒店ID失败: {}", trip.getHotelIds());
                }
            }

            if (StringUtils.isNotEmpty(trip.getRestIds())) {
                try {
                    List<String> ids = JSON.parseArray(trip.getRestIds(), String.class);
                    restaurantIds.addAll(ids);
                } catch (Exception e) {
                    log.warn("解析餐厅ID失败: {}", trip.getRestIds());
                }
            }

            if (StringUtils.isNotEmpty(trip.getWordIds())) {
                try {
                    List<String> ids = JSON.parseArray(trip.getWordIds(), String.class);
                    wordIds.addAll(ids);
                } catch (Exception e) {
                    log.warn("解析常用语ID失败: {}", trip.getWordIds());
                }
            }

            // 4. 批量获取坐标信息
            List<TripMapDataVO.ScenicCoordinateVO> scenicList = getScenicCoordinates(scenicIds);
            List<TripMapDataVO.HotelCoordinateVO> hotelList = getHotelCoordinates(hotelIds);
            List<TripMapDataVO.RestaurantCoordinateVO> restaurantList = getRestaurantCoordinates(restaurantIds);
            List<TripMapDataVO.WordCoordinateVO> wordList = getWordCoordinates(wordIds);

            // 5. 组装返回数据
            TripMapDataVO mapData = new TripMapDataVO();
            mapData.setTripInfo(tripDetail);
            mapData.setScenicList(scenicList);
            mapData.setHotelList(hotelList);
            mapData.setRestaurantList(restaurantList);
            mapData.setWordList(wordList);

            return mapData;
        } catch (Exception e) {
            log.error("获取行程地图数据失败", e);
            throw new RuntimeException("获取行程地图数据失败", e);
        }
    }

    @Override
    public List<TripMapDataVO.ScenicCoordinateVO> getScenicCoordinates(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            // 批量查询景点信息
            List<BizScenic> scenics = bizScenicService.listByIds(ids);
            
            return scenics.stream()
                    .filter(scenic -> scenic.getLongitude() != null && scenic.getLatitude() != null)
                    .map(scenic -> {
                        TripMapDataVO.ScenicCoordinateVO vo = new TripMapDataVO.ScenicCoordinateVO();
                        vo.setId(scenic.getId());
                        vo.setScenicName(scenic.getScenicName());
                        vo.setLongitude(scenic.getLongitude());
                        vo.setLatitude(scenic.getLatitude());
                        vo.setRegionAddress(scenic.getRegionAddress());
                        return vo;
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("批量获取景点坐标失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<TripMapDataVO.HotelCoordinateVO> getHotelCoordinates(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            // 批量查询酒店信息
            List<BizHotel> hotels = bizHotelService.listByIds(ids);
            
            return hotels.stream()
                    .filter(hotel -> hotel.getLongitude() != null && hotel.getLatitude() != null)
                    .map(hotel -> {
                        TripMapDataVO.HotelCoordinateVO vo = new TripMapDataVO.HotelCoordinateVO();
                        vo.setId(hotel.getId());
                        vo.setHotelName(hotel.getHotelName());
                        vo.setLongitude(hotel.getLongitude());
                        vo.setLatitude(hotel.getLatitude());
                        vo.setRegionAddress(hotel.getRegionAddress());
                        return vo;
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("批量获取酒店坐标失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<TripMapDataVO.RestaurantCoordinateVO> getRestaurantCoordinates(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            // 批量查询餐厅信息
            List<BizRestaurant> restaurants = bizRestaurantService.listByIds(ids);
            
            return restaurants.stream()
                    .filter(restaurant -> restaurant.getLongitude() != null && restaurant.getLatitude() != null)
                    .map(restaurant -> {
                        TripMapDataVO.RestaurantCoordinateVO vo = new TripMapDataVO.RestaurantCoordinateVO();
                        vo.setId(restaurant.getId());
                        vo.setRestName(restaurant.getRestName());
                        vo.setLongitude(restaurant.getLongitude());
                        vo.setLatitude(restaurant.getLatitude());
                        vo.setRegionAddress(restaurant.getRegionAddress());
                        return vo;
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("批量获取餐厅坐标失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<TripMapDataVO.WordCoordinateVO> getWordCoordinates(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            // 批量查询常用语信息
            List<BizWord> words = bizWordService.listByIds(ids);
            
            return words.stream()
                    .filter(word -> word.getLongitude() != null && word.getLatitude() != null)
                    .map(word -> {
                        TripMapDataVO.WordCoordinateVO vo = new TripMapDataVO.WordCoordinateVO();
                        vo.setId(word.getId());
                        vo.setWordTitle(word.getWordTitle());
                        vo.setLongitude(word.getLongitude());
                        vo.setLatitude(word.getLatitude());
                        vo.setWordScence(word.getWordScence());
                        return vo;
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("批量获取常用语坐标失败", e);
            return new ArrayList<>();
        }
    }

    private void processPriceInfo(BizTripAiContent aiContent, TripDetailVO detailVO) {
        if (aiContent == null || StringUtils.isEmpty(aiContent.getAiPriceRule())) {
            detailVO.setPriceMode(null);
            return;
        }

        try {
            JSONObject priceRule = JSON.parseObject(aiContent.getAiPriceRule());
            String priceMode = priceRule.getString("priceMode");
            detailVO.setPriceMode(priceMode);

            if (StringUtils.isEmpty(priceMode) || "none".equals(priceMode)) {
                return;
            }

            // 构建价格详情对象
            TripDetailVO.PriceInfo priceInfo = new TripDetailVO.PriceInfo();

            // 处理基本信息
            if (!StringUtils.isEmpty(aiContent.getCustInfo())) {
                JSONObject customInfo = JSON.parseObject(aiContent.getCustInfo());
                TripDetailVO.BasicInfo basicInfo = new TripDetailVO.BasicInfo();
                basicInfo.setDepartureDate(customInfo.getString("departureDate"));
                basicInfo.setAdultCount(customInfo.getInteger("adultCount"));
                basicInfo.setChildCount(customInfo.getInteger("childCount"));
                basicInfo.setRoomCount(customInfo.getInteger("roomCount"));
                priceInfo.setBasicInfo(basicInfo);
            }

            BigDecimal totalPrice = BigDecimal.ZERO;
            // 根据不同报价模式处理
            switch (priceMode) {
                case "custInput":
                    // 处理填空报价
                    String custInputContent = priceRule.getString("custInputContent");
                    priceInfo.setCustInputContent(custInputContent);
                    // 填空报价模式下不设置总价
                    detailVO.setTotalPrice(null);
                    break;
                    
                case "detailed":
                    // 处理分项报价
                    List<TripDetailVO.PriceCategory> categories = new ArrayList<>();
                    String[] priceTypes = {"scenic", "hotel", "traffic", "restaurant", "commonFee", "cust"};
                    String[] priceNames = {"景点", "酒店", "交通", "餐饮", "通用费用", "其他"};
                    
                    for (int i = 0; i < priceTypes.length; i++) {
                        if (priceRule.containsKey(priceTypes[i])) {
                            TripDetailVO.PriceCategory category = processPriceCategory(
                                    priceRule.getJSONArray(priceTypes[i]), priceTypes[i], priceNames[i]);
                            if (category != null) {
                                categories.add(category);
                                totalPrice = totalPrice.add(calculateCategoryTotal(category));
                            }
                        }
                    }
                    priceInfo.setCategories(categories);
                    break;
                    
                case "simple":
                    // 处理简易报价
                    if (priceRule.containsKey("priceList")) {
                        List<TripDetailVO.SimplePrice> simplePrices = new ArrayList<>();
                        JSONArray priceList = priceRule.getJSONArray("priceList");

                        for (int i = 0; i < priceList.size(); i++) {
                            JSONObject item = priceList.getJSONObject(i);
                            TripDetailVO.SimplePrice simplePrice = new TripDetailVO.SimplePrice();
                            simplePrice.setCategory(item.getString("category"));
                            simplePrice.setUnitPrice(item.getBigDecimal("unitPrice"));
                            simplePrice.setQuantity(item.getInteger("quantity"));
                            BigDecimal itemTotal = simplePrice.getUnitPrice()
                                    .multiply(new BigDecimal(simplePrice.getQuantity()));
                            simplePrice.setTotal(itemTotal);
                            simplePrices.add(simplePrice);
                            totalPrice = totalPrice.add(itemTotal);
                        }
                        priceInfo.setSimplePrices(simplePrices);
                    }
                    break;
            }

            // 处理费用包括项
            if (priceRule.containsKey("includedFees")) {
                List<TripDetailVO.IncludedFee> includedFees = new ArrayList<>();
                JSONArray includedFeesArray = priceRule.getJSONArray("includedFees");

                for (int i = 0; i < includedFeesArray.size(); i++) {
                    JSONObject item = includedFeesArray.getJSONObject(i);
                    TripDetailVO.IncludedFee includedFee = new TripDetailVO.IncludedFee();
                    includedFee.setName(item.getString("name"));
                    includedFee.setRemark(item.getString("remark"));
                    includedFees.add(includedFee);
                }
                priceInfo.setIncludedFees(includedFees);
            }

            // 处理费用不含项
            if (priceRule.containsKey("excludedFees")) {
                List<TripDetailVO.ExcludedFee> excludedFees = new ArrayList<>();
                JSONArray excludedFeesArray = priceRule.getJSONArray("excludedFees");

                for (int i = 0; i < excludedFeesArray.size(); i++) {
                    JSONObject item = excludedFeesArray.getJSONObject(i);
                    TripDetailVO.ExcludedFee excludedFee = new TripDetailVO.ExcludedFee();
                    excludedFee.setName(item.getString("name"));
                    excludedFee.setRemark(item.getString("remark"));
                    excludedFees.add(excludedFee);
                }
                priceInfo.setExcludedFees(excludedFees);
            }

            detailVO.setPriceInfo(priceInfo);
            detailVO.setTotalPrice(totalPrice);

        } catch (Exception e) {
            log.error("Process price info failed", e);
            detailVO.setPriceMode(null);
        }
    }

    private TripDetailVO.PriceCategory processPriceCategory(JSONArray items, String type, String name) {
        if (items == null || items.isEmpty()) {
            return null;
        }

        List<TripDetailVO.PriceItem> priceItems = new ArrayList<>();

        for (int i = 0; i < items.size(); i++) {
            JSONObject item = items.getJSONObject(i);
            TripDetailVO.PriceItem priceItem = new TripDetailVO.PriceItem();
            priceItem.setName(item.getString("title"));
            priceItem.setUnitPrice(item.getBigDecimal("bizPrice"));
            priceItem.setUnit(item.getString("unit"));
            priceItem.setCustomUnit(item.getString("customUnit"));
            priceItem.setQuantity(item.getInteger("quantity"));
            priceItem.setRemark(item.getString("remark"));

            // 计算小计
            BigDecimal total = priceItem.getUnitPrice()
                    .multiply(new BigDecimal(priceItem.getQuantity()));
            priceItem.setTotal(total);

            priceItems.add(priceItem);
        }

        if (priceItems.isEmpty()) {
            return null;
        }

        TripDetailVO.PriceCategory category = new TripDetailVO.PriceCategory();
        category.setType(type);
        category.setName(name);
        category.setItems(priceItems);
        return category;
    }

    private BigDecimal calculateCategoryTotal(TripDetailVO.PriceCategory category) {
        return category.getItems().stream()
                .map(TripDetailVO.PriceItem::getTotal)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}