<template>
  <a-spin :spinning="loading">
    <a-card :bordered="false">
      <a-form layout="horizontal" class="form">
        <a-form-item label="餐厅名称" class="my-form-item">
          <a-input v-model="model.restName" placeholder="餐厅名称" style='width: 380px;'/>
          <a-input v-model="model.restSimpleName" placeholder="简称" style='width: 110px;margin-left: 10px;'  />
        </a-form-item>
        <a-form-item label="特色简述" class="my-form-item" >
          <a-input v-model="model.advantageDesc" placeholder="一句话描述酒店特色" />
        </a-form-item>
        <a-form-item label="餐厅介绍" class="my-form-item">
          <a-textarea
            placeholder="用于餐厅图文详细介绍素材，支持 # 插入图片"
            class="textarea"
            :auto-size="{ minRows: 6, maxRows: 30 }"
            v-model="model.restDesc"
          ></a-textarea>
        </a-form-item>
        <!--自动生成图文介绍和导出word时是否自动填写餐厅介绍素材-->
        <a-form-item label="自动填充" class="my-form-item">
          <a-switch default-checked v-model="model.autoFillingDesc" /><span style="color:#C0C0C0">  行程图文介绍是否自动填充该餐厅</span>
         </a-form-item>
        <a-form-item label="行程介绍" class="my-form-item">
          <a-textarea
            placeholder="用于行程时间表展现，支持 * 分割多个子行程"
            class="textarea"
            v-model="model.tripDesc"
            :auto-size="{ minRows: 3, maxRows: 30 }"
          ></a-textarea>
        </a-form-item>
        <a-form-item label="最低餐标" class="my-form-item" >
           <a-input-number v-model="model.price" :defaultValue="300" :min="0" :max="20000" :step="50"/><span style="color:#C0C0C0"> 不计入成本的餐填0</span>
        </a-form-item>  
         <a-form-item label="费用备注" class="my-form-item">
          <a-textarea
            placeholder="报价说明, 用于word导出报价部分"
            class="textarea"
            :auto-size="{ minRows: 3, maxRows: 30 }"
            v-model="model.priceRemark"
          ></a-textarea>
        </a-form-item>
        <a-form-item label="搜索指数" class="my-form-item" >
          <a-input-number style="width:100px;" placeholder="搜索指数" v-model="model.searchIndex" />
        </a-form-item>        
        <a-form-item class="my-form-item" label="地理位置">
          <a-cascader
            popupClassName="media-edit-cascader"
            @change="onAreaChange"
            change-on-select
            :options="areaData"
            v-model="area"
            :showSearch="true"
            placeholder="请选择"
          />
        </a-form-item>
        <!-- <a-form-item label="详细地址" class="my-form-item">
          <a-input v-model="model.regionAddress" placeholder="请输入详细地址" />
        </a-form-item> -->
        <a-form-item label="地理坐标" class="my-form-item">
          <a-input-number placeholder="经度" v-model="model.longitude" style="width: 120px;" :precision="6" />
          <span style="margin: 0 10px;">，</span>
          <a-input-number placeholder="纬度" v-model="model.latitude" style="width: 120px;" :precision="6" />
          <a-button type="primary" style="margin-left: 10px;" @click="openMapPicker">
            <a-icon type="environment" />地图选点
          </a-button>
        </a-form-item>        
        <!-- <a-form-item label="特色标签" class="my-form-item checkbox-area">
          <a-checkbox-group v-model="model.restTag">
            <div
              style="display: inline-block;margin-right:8px;"
              v-for="(item,index) in this.restTagList"
              :key="index"
            >
              <a-checkbox :name="item.itemText" :value="item.itemText">{{item.itemText}}</a-checkbox>
            </div>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item label="菜系" class="my-form-item" >
         <j-dict-select-tag style="width:100px;"
            v-model="model.restFoodType"
            placeholder="请选择菜系"
            dictCode="biz_rest_foot_type"
          />
        </a-form-item>         -->
        <a-form-item label="图片" class="my-form-item-upload" >
          <div class="clearfix">
            <upload-image :key="'rest_' + model.id" @getImageId="getImageId" @delImageId="delImageId" :multiple="15" :img="upload.fileList" @draggable="handleDraggable" v-if="loadFinished"></upload-image>
          </div>
        </a-form-item>
        <a-form-item label="封面图" class="my-form-item-upload" >
          <div class="clearfix">
            <upload-image :key="'rest_cover_' + model.id" @getImageId="getImageIdCover" @delImageId="delImageIdCover" :multiple="2"  :max-file-size="5" :img="upload.coverFileList" @draggable="handleDraggableCover" v-if="loadFinished"></upload-image>
          </div>
        </a-form-item>
        <a-form-item label="附件" class="my-form-item-upload">
          <div class="attach-box">
            <a-upload name="file" :action="uploadUrl" :fileList="fileUpload.fileList" :beforeUpload="beforeFileUpload"
              @change="handleFileChange" :showUploadList="{ showRemoveIcon: true, showPreviewIcon: true }">
              <a-button>
                <a-icon type="upload" />
                上传附件
              </a-button>
            </a-upload>
          </div>
        </a-form-item>
        <a-form-item label="状态" class="my-form-item" >
          <j-dict-select-tag style="width:100px;"
            v-model="model.status"
            placeholder="状态"
            dictCode="biz_rest_status"
          />
        </a-form-item>  
        <!--暂时屏蔽网络链接      
        <a-form-item label="网络链接" class="my-form-item" >
          <a-input v-model="model.thirdUrl" placeholder="网络链接" />
        </a-form-item>
        -->
        <a-form-item class="my-center">
          <button type="button" class="ant-btn ant-btn-primary" @click="save"><span>确 定</span></button>
          <button type="button" class="ant-btn" style="margin-left:10px;" @click="this.closeCurrent"><span>关 闭</span></button>
        </a-form-item>
        
      </a-form>

    </a-card>
    
    <!-- 地图选点组件 -->
    <map-picker 
      ref="mapPicker"
      :initial-longitude="model.longitude"
      :initial-latitude="model.latitude"
      :initial-keyword="model.restName"
      search-region="全国"
      search-category="餐厅,饭店,美食,小吃"
      @select="onMapSelect"
      @address-info="onAddressInfo"
    />
  </a-spin>
</template>

<script>
import { deleteAction, getAction, postAction, putAction } from '@/api/manage';
import { RouterLinkMixinConfig } from '@/mixins/RouterLinkMixinConfig';
import moment from 'moment'
import validataFun from '@/utils/validate.js'
import ChineseDistricts from '@/assets/distpicker.data.min.js'
import { axios } from '@/utils/request';
import UploadImage from '@comp/UploadImage/UploadImage';
import MapPicker from '@/components/MapPicker/MapPicker.vue'

// 处理地区数组
function getCityArr(cityJson) {
  let Pobj = cityJson[1];
  let Pvalue = Object.keys(Pobj);

  let option = Pvalue.map(item => {
    let arr = { value: Pobj[item], label: Pobj[item] };
    if (Object.keys(cityJson).includes(item)) {
      arr.children = getCityArrChild(cityJson, item);
    }
    return arr;
  });
  return option;
}
function getCityArrChild(cityJson, key) {
  let Cobj = cityJson[key];
  let Cvalue = Object.keys(Cobj);

  let Coption = Cvalue.map(item => {
    let Carr = { value: Cobj[item], label: Cobj[item] };
    if (Object.keys(cityJson).includes(item)) {
      Carr.children = getCityArrChild(cityJson, item);
    }
    return Carr;
  });
  return Coption;
}

export default {
  name: 'BizResuaurantEdit',
  mixins: [RouterLinkMixinConfig],
  inject:['closeCurrent','onCloseCurrent'],
  components: {
    validataFun,ChineseDistricts,UploadImage,MapPicker
  },
  activated() {
    this.loading = false;
    this.fetchPageData()
    if(this.$route.params.id){
      this.model.id=this.$route.params.id;
      this.getEditData();
    }else{
      this.loadFinished = true;
    }
  },
  data() {
    return {
      model:{
        id: null,
        advantageDesc:null,
        restDesc:null,
        autoFillingDesc:false,
        tripDesc:null,
        searchIndex:0,
        region: null,
        priceRemark:null,
        regionAddress: null,
        regionArea: null,
        regionCity: null,
        regionCountry: null,
        regionProvince: null,
        longitude: null,
        latitude: null,
        restName: null,
        restSimpleName: null,
        status:"1",
        price:300,
        restTag:[],
        imgUrl:null,
        coverImgUrl:null,
        attach: null,
        thirdUrl:null,
      },
      resetModel:{
        id: null,
        advantageDesc:null,
        autoFillingDesc:false,
        restDesc:null,
        tripDesc:null,
        searchIndex:0,
        region: null,
        priceRemark:null,
        regionAddress: null,
        regionArea: null,
        regionCity: null,
        regionCountry: null,
        regionProvince: null,
        longitude: null,
        latitude: null,
        restName: null,
        restSimpleName: null,
        status:"1",
        price:300,
        restTag:[],
        imgUrl:null,
        coverImgUrl:null,
        attach: null,
        thirdUrl:null,
      },
      upload:{
        uploadUrl:"/file/upload",
        fileList:[],
        coverFileList:[],
      },
       //附件
       fileUpload: {
        fileList: [],
      },
      //景区标签
      restTagList:[],
      //全部区域数据
      areaData:{},
      //选择出来的区域数组，省-市-县
      area:[],
      loading: true,
      loadFinished: false,
      url: {
        add: "/biz/bizRestaurant/add",
        get: "/biz/bizRestaurant/queryById",
        edit: "/biz/bizRestaurant/edit",
      },
    };
  },
  computed:{
    uploadUrl() {
      let url=(axios.defaults.baseURL||"") + '/file/upload'
      return url
    }
  },
  created(){    
    // this.onCloseCurrent(this.$route.path,this.reset)
    this.onCloseCurrent(this.$route.path,()=>{
      this.reset()
      this.$router.push({path:"/biz/bizRestaurant/list"})
    })
    this.areaData = getCityArr(ChineseDistricts);
  },
  methods: {
    onAreaChange(value){
      this.model.regionProvince=value[0];
      this.model.regionCity=value[1];
      this.model.regionArea=value[2];
      this.model.regionCountry='中国';
      this.model.region=value.join("-");
    },
    /*跳转清理缓存*/
    cacheSession() {
      const cacheSSg = window.location.href.split('#')[1];
      sessionStorage.removeItem(cacheSSg);
      this.$store.commit('SET_SKIP_ROUTE', true);
    },
    getEditData() {
      //加载编辑数据
      getAction(this.url.get,{ id: this.model.id })
        .then(res => {
          if (res.success) {
            this.model= res.result;
            this.model.createTime=null;
            this.model.updateTime=null;
            //设置区域
            this.area=[this.model.regionProvince,this.model.regionCity,this.model.regionArea];
            //设置图片
            if(this.model.imgUrl!=null && this.model.imgUrl.length>0){
              this.upload.fileList=this.model.imgUrl.split(",");
            }
            //设置封面图片
            if (this.model.coverImgUrl != null && this.model.coverImgUrl.length > 0) {
              this.upload.coverFileList = this.model.coverImgUrl.split(",");
            }
            // 文件回显
            if (res.result.attach != null && res.result.attach.length > 0) {
              this.fileUpload.fileList = JSON.parse(res.result.attach);
            }
            this.loadFinished = true
            //操作
            // setTimeout(() => {
            //   this.fetchInitPos(this.advert.mediaType)
            // }, 300);
          } else {
            this.$message.warning(res.message || '请求错误');
          }
        })
        .catch(res => {
          this.$message.warning(res.message || '网络错误');
        });
    },
    //获取上传的图片
    getImageId: function (val) {
      this.upload.fileList = this.upload.fileList.concat(val)
    },
    //删除图片
    delImageId: function (val) {
      this.upload.fileList= this.upload.fileList.filter((x) => x !== val);
    },
    //获取到重新排序后的图片
    handleDraggable (e) {
      this.upload.fileList = e;
    },    
    
    //获取上传的封面图片
    getImageIdCover: function (val) {
      this.upload.coverFileList = this.upload.coverFileList.concat(val)
    },
    //删除封面图片
    delImageIdCover: function (val) {
      this.upload.coverFileList= this.upload.coverFileList.filter((x) => x !== val);
    },
    //获取到重新排序后的封面图片
    handleDraggableCover (e) {
      this.upload.coverFileList = e;
    },
    // 上传文件前的校验
    beforeFileUpload(file) {
      const MAX_FILE_SIZE_MB = 10; // 10MB 大小限制
      const VALID_TYPES = [
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ];

      const isSizeValid = file.size / 1024 / 1024 <= MAX_FILE_SIZE_MB;
      const isTypeValid = VALID_TYPES.includes(file.type);

      if (!isSizeValid) {
        this.$message.error('文件大小不能超过10MB');
        return false;
      }

      if (!isTypeValid) {
        this.$message.error('只允许上传PPTX和DOCX文件');
        return false;
      }

      const pptCount = this.fileUpload.fileList.filter(file => file.type === 'application/vnd.openxmlformats-officedocument.presentationml.presentation').length;
      const docCount = this.fileUpload.fileList.filter(file => file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document').length;

      if (file.type === 'application/vnd.openxmlformats-officedocument.presentationml.presentation' && pptCount >= 1) {
        this.$message.error('只允许上传一个PPT文件');
        return false;
      }

      if (file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' && docCount >= 1) {
        this.$message.error('只允许上传一个Word文件');
        return false;
      }

      return true;
    },
    // 文件上传
    handleFileChange({ fileList }) {
      const MAX_FILE_SIZE_MB = 10;
      const VALID_TYPES = [
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ];

      // 过滤掉不符合大小和类型的文件
      this.fileUpload.fileList = fileList.filter(file => {
        const isSizeValid = file.size / 1024 / 1024 <= MAX_FILE_SIZE_MB;
        const isTypeValid = VALID_TYPES.includes(file.type);
        return isSizeValid && isTypeValid;
      }).map(file => {
        // Ensure the URL is correctly set
        let url = file.url;
        if (!url && file.response && file.response.success) {
          url = file.response.result;
        }
        return {
          name: file.name,
          url,
          type: file.type,
          size: file.size,
          status: file.status || 'done',
          uid: file.uid
        };
      });

      // 确保只保留一个PPT文件和一个Word文件
      const pptFiles = this.fileUpload.fileList.filter(file => file.type === 'application/vnd.openxmlformats-officedocument.presentationml.presentation');
      const docFiles = this.fileUpload.fileList.filter(file => file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');

      this.fileUpload.fileList = [...pptFiles.slice(0, 1), ...docFiles.slice(0, 1)];

      // 更新 model.attach
      this.model.attach = JSON.stringify(this.fileUpload.fileList);
    },      
      
    fetchPageData() {
      //加载标签数据
      getAction(`/sys/dictItem/all`,{ dictCode: "biz_rest_tag" })
        .then(res => {
          if (res.success) {
            this.restTagList= res.result;

          } else {
            this.$message.warning(res.message || '请求错误');
          }
        })
        .catch(res => {
          this.$message.warning(res.message || '网络错误');
        });
    },
    save(){
       if(!this.model.restName){
        this.$message.error('餐厅名称必须填写');
        return false;
      }
      if(this.model.price===null){
        this.$message.error('最低餐标必须填写，如不计入成本，可填写0');
        return false;
      }
      //处理图片链接
      if(this.upload.fileList!=null && this.upload.fileList.length>0){
        this.model.imgUrl=this.upload.fileList.join(",")
      }else{
        this.model.imgUrl="";
      }
      //处理封面图片链接
      if(this.upload.coverFileList!=null && this.upload.coverFileList.length>0){
        this.model.coverImgUrl=this.upload.coverFileList.join(",")
      }else{
        this.model.coverImgUrl="";
      }
      // 处理附件
      if (this.fileUpload.fileList && this.fileUpload.fileList.length > 0) {
        this.model.attach = JSON.stringify(this.fileUpload.fileList);
      }
      if(this.model.id&&this.model.id>0){
        this.edit();
      }else{
        this.add();
      }
    },
    add(){
      this.loading = true
      let params = Object.assign({},this.model)
      params.restTag=JSON.stringify(this.model.restTag||[])
      postAction(this.url.add,params)
        .then(res => {
          if (res.success) {
            this.$message.success(res.message || '操作成功');
            this.closeCurrent()
          } else {
            this.$message.error(res.message || '请求错误');
          }
        })
        .catch(res => {
          this.$message.error(res.message || '网络错误');
        })
        .finally(() => {
          this.loading = false;
        });
    },
    edit(){
      this.loading = true
      let params = Object.assign({},this.model)
      params.restTag=JSON.stringify(this.model.restTag||[])
      putAction(this.url.edit,params)
        .then(res => {
          if (res.success) {
            this.$message.success(res.message || '操作成功');
            this.closeCurrent()
            //操作
          } else {
            this.$message.warning(res.message || '请求错误');
          }
          this.loading = false;
        }).catch(res => {
          this.$message.warning(res.message || '网络错误');
        });
    },    
    reset(){
      this.model=Object.assign({},this.resetModel)
      this.area=[]
      this.upload.fileList=[]
      this.upload.coverFileList=[]   
      this.fileUpload.fileList=[]
      this.$destroy('UploadImage') 
    },
    // 打开地图选点器
    openMapPicker() {
      this.$refs.mapPicker.show();
    },
    
    // 地图选点回调
    onMapSelect(coordinates) {
      this.model.longitude = coordinates.longitude;
      this.model.latitude = coordinates.latitude;
      this.$message.success('坐标设置成功');
    },
    
    // 地址信息回调
    onAddressInfo(addressInfo) {
      if (addressInfo.address && !this.model.regionAddress) {
        this.model.regionAddress = addressInfo.address;
      }
      if (!this.model.restName && addressInfo.title) {
        this.$confirm({
          title: '发现餐厅信息',
          content: `是否使用搜索到的餐厅名称："${addressInfo.title}"？`,
          onOk: () => {
            this.model.restName = addressInfo.title;
          }
        });
      }
    }
  }
};
</script>
<style lang="scss" scoped>
::v-deep .ant-card-body {
  padding-left: 15px;
}

.form {
  padding: 0 100px;
}
.my-form-item{
  display: flex;
  textarea{
    width: 500px;
  }
  input{
    width: 500px;
  }
  .btn-left{
    margin-left: 20px;
  }
  a-cascader{
    width:200px;
  }
}
.checkbox-area{
  width: 600px;
  flex-wrap: wrap ;
  justify-content:flex-start;
}
 .my-center{
   text-align: center;
  .btn-left{
    margin-left: 20px;
  }
 }
.title {
  display: flex;
  flex-direction: row;
  font-size: 16px;
  margin: 20px 0 10px;
  align-items: center;
  .spot {
    width: 6px;
    height: 16px;
    margin: 0 8px 0 0;
    background: #5ac78a;
    border-radius: 6px;
  }
}
.my-form-item-upload{
  width: 600px;
}
.ImgModal{
   ::v-deep .ant-modal-body{
     padding: 24px !important;
   }
   ::v-deep .ant-modal-close{
     .ant-modal-close-x {
      display: block;
      width: 36px;
      height: 36px;
      font-size: 16px;
      font-style: normal;
      line-height: 36px;
      text-align: center;
      text-transform: none;
      text-rendering: auto;
    }
   }
  }
</style>
